<template>
    <section>
        <sectionTitle title="租金信息"></sectionTitle>
        <a-card :bordered="false" :body-style="{ padding: '16px 0' }">
            <a-table :scroll="{ x: 1500 }" :data="tableData" :pagination="false" :bordered="{ cell: true }" summary summary-text="合计">
                <template #columns>
                    <a-table-column title="租期" data-index="rentDate" :width="170" align="center">
                        <template #cell="{ record }">
                            {{ dayjs(record.startDate).format('YYYY-MM-DD') }} 至 {{ dayjs(record.endDate).format('YYYY-MM-DD') }}
                        </template>
                    </a-table-column>
                    <a-table-column title="商户" data-index="customerName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="费项" data-index="subjectName" :width="100" align="center" :ellipsis="true"
                        :tooltip="true" />
                    <a-table-column title="应收日期" data-index="receivableDate" :width="100" align="center">
                        <template #cell="{ record }">
                            {{ record.receivableDate }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单总额（元）" data-index="totalAmount" :width="120" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.totalAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="优惠金额（元）" data-index="discountAmount" :width="120" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.discountAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单应收金额（元）" data-index="actualReceivable" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单已收金额（元）" data-index="receivedAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.receivedAmount) }}
                        </template>
                    </a-table-column>
                    <a-table-column title="账单剩余应收金额（元）" data-index="remainingAmount" :width="140" align="right">
                        <template #cell="{ record }">
                            {{ formatAmount(record.actualReceivable - record.receivedAmount) }}
                        </template>
                    </a-table-column>
                </template>
                <template #summary-cell="{ column }">
                    <template v-if="column.dataIndex === 'rentDate'">合计</template>
                    <template v-if="column.dataIndex === 'totalAmount'">{{ formatAmount(totalAmountSum) }}</template>
                    <template v-if="column.dataIndex === 'discountAmount'">{{ formatAmount(discountAmountSum) }}</template>
                    <template v-if="column.dataIndex === 'actualReceivable'">{{ formatAmount(actualReceivableSum) }}</template>
                    <template v-if="column.dataIndex === 'receivedAmount'">{{ formatAmount(receivedAmountSum) }}</template>
                    <template v-if="column.dataIndex === 'remainingAmount'">{{ formatAmount(remainingAmountSum) }}</template>
                </template>
            </a-table>
        </a-card>
    </section>
</template>

<script setup lang="ts">
import sectionTitle from '@/components/sectionTitle/index.vue'
import { useContractStore } from '@/store/modules/contract'
import dayjs from 'dayjs';
import { ContractCostDTO, ContractAddDTO, ContractVo } from '@/api/contract';

// 金额格式化
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return ''
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })
}

const unitMap = new Map([
    [1, '元/平方米/月'],
    [2, '元/月']
])

const contractStore = useContractStore()
const contractCosts = computed(() => contractStore.contractDetailCosts as ContractVo)
const contractData = computed(() => contractStore.contractDetail as ContractAddDTO)

const tableData = ref<any[]>([])

/**
 * 监听应收计划计算结果
 * 1. 当合同为标准合同时，需要合并costs中所有costType为租金且period相同的数据
 *    - rentDate取租金同一期中最早的日期(startDate)和最晚的日期(endDate)，格式为YYYY-MM-DD 至 YYYY-MM-DD
 *    - customerName取租金同一期第一条数据的customerName，因为都相同
 *    - subjectName取租金同一期第一条数据的subjectName，因为都相同
 *    - receivableDate取租金同一期第一条数据的receivableDate，因为都相同
 *    - totalAmount取租金同一期所有数据的totalAmount之和
 *    - discountAmount取租金同一期所有数据的discountAmount之和
 *    - actualReceivable取租金同一期所有数据的actualReceivable之和
 *    - receivedAmount取租金同一期所有数据的receivedAmount之和
 *    - remainingAmount为actualReceivable - receivedAmount
 * 2. 当合同为非标准合同时，直接使用costs中的数据
 *    - rentDate 格式为 startDate 至 endDate
 *    - remainingAmount为actualReceivable - receivedAmount
 */
watch(contractCosts, (newVal) => {
    if (newVal.costs && newVal.costs.length > 0) {
        const rentCosts = newVal.costs.filter((item: any) => item.costType === 2)
        if (contractData.value.contractMode === 0) {
            if (rentCosts.length > 0) {
                const rentCostsGroup = rentCosts.reduce((acc: any, item: any) => {
                    if (!acc[item.period]) {
                        acc[item.period] = []
                    }
                    acc[item.period].push(item)
                    return acc
                }, {} as Record<number, ContractCostDTO[]>)
                
                tableData.value = Object.values(rentCostsGroup).map((item: any) => {
                    const startDate = dayjs(item[0].startDate).format('YYYY-MM-DD')
                    const endDate = dayjs(item[item.length - 1].endDate).format('YYYY-MM-DD')
                    const customerName = item[0].customerName ?? ''
                    const subjectName = item[0].subjectName ?? ''
                    const receivableDate = item[0].receivableDate ? dayjs(item[0].receivableDate).format('YYYY-MM-DD') : ''
                    const totalAmount = item.reduce((acc: any, item: any) => acc + (item.totalAmount ?? 0), 0)
                    const discountAmount = item.reduce((acc: any, item: any) => acc + (item.discountAmount ?? 0), 0)
                    const actualReceivable = item.reduce((acc: any, item: any) => acc + (item.actualReceivable ?? 0), 0)
                    const receivedAmount = item.reduce((acc: any, item: any) => acc + (item.receivedAmount ?? 0), 0)
                    return {
                        startDate,
                        endDate,
                        customerName,
                        subjectName,
                        receivableDate,
                        totalAmount,
                        discountAmount,
                        actualReceivable,
                        receivedAmount
                    }
                })
            }
        } else {
            tableData.value = rentCosts.map((item: any) => {
                return {
                    ...item,
                    startDate: item.startDate,
                    endDate: item.endDate
                }
            })
        }
    } else {
        tableData.value = []
    }
}, { deep: true, immediate: true })

const totalAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.totalAmount || 0), 0))
const discountAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.discountAmount || 0), 0))
const actualReceivableSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.actualReceivable || 0), 0))
const receivedAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + (item.receivedAmount || 0), 0))
const remainingAmountSum = computed(() => tableData.value.reduce((sum, item) => sum + ((item.actualReceivable || 0) - (item.receivedAmount || 0)), 0))
</script>

<style lang="less" scoped>
.detail-text {
    color: #333;
}
</style>