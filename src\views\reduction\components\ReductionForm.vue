<template>
    <a-drawer
        v-model:visible="visible"
        :title="drawerTitle"
        class="common-drawer"
        :footer="true"
        @cancel="handleCancel"
    >
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">{{ readOnlyMode ? '关闭' : '取消' }}</a-button>
                <template v-if="!readOnlyMode">
                    <a-button @click="handleSave" :loading="loading">暂存</a-button>
                    <a-button type="primary" @click="handleSubmit" :loading="loading">保存&发起审批</a-button>
                </template>
            </a-space>
        </template>

        <div class="reduction-form">
            <a-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                label-align="right"
                layout="horizontal"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                auto-label-width
            >
                <!-- 基本信息 -->
                <div class="form-section">
                    <SectionTitle title="基本信息" style="margin-bottom: 16px;" />
                    <!-- 第一行：项目、承租人、合同、房间号 -->
                    <a-row :gutter="16">
                        <a-col :span="6">
                            <a-form-item field="projectId" label="项目" required>
                                <ProjectTreeSelect
                                    v-model="formData.projectId"
                                    :min-level="4"
                                    :disabled="readOnlyMode"
                                    @change="handleProjectChange"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="customerId" label="承租人" required>
                                <a-select
                                    v-model="formData.customerId"
                                    placeholder="请选择承租人"
                                    :disabled="readOnlyMode"
                                    filterable
                                    allow-search
                                    @change="handleCustomerChange"
                                >
                                    <a-option v-for="customer in customerOptions" :key="customer.id" :value="customer.id">
                                        {{ customer.customerName }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item field="contractId" label="合同" required>
                                <a-select
                                    v-model="formData.contractId"
                                    placeholder="请选择合同"
                                    :disabled="readOnlyMode"
                                    filterable
                                    allow-search
                                    @change="handleContractChange"
                                >
                                    <a-option v-for="contract in contractOptions" :key="contract.id" :value="contract.id">
                                        {{ contract.contractNo }}（{{ contract.statusText }}）
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <!-- 第二行：合同周期、合同用途、租赁单元（占2格） -->
                    <a-row :gutter="16">
                        <a-col :span="6" v-if="!readOnlyMode">
                            <a-form-item field="roomId" label="房间号">
                                <a-select
                                    v-model="formData.roomId"
                                    placeholder="请选择房间号"
                                    :disabled="readOnlyMode"
                                    filterable
                                    @change="handleRoomChange"
                                >
                                    <a-option v-for="room in roomOptions" :key="room.id" :value="room.id">
                                        {{ room.roomName }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="contractPeriod" label="合同周期">
                                <a-input v-model="formData.contractPeriod" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="contractPurposeText" label="合同用途">
                                <a-input v-model="formData.contractPurposeText" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="readOnlyMode?12:6">
                            <a-form-item field="roomName" label="租赁单元">
                                <a-input v-model="formData.roomName" disabled />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <!-- 第三行：申请类型、申请原因 -->
                    <a-row :gutter="16">
                        <a-col :span="6">
                            <a-form-item field="type" label="申请类型" required>
                                <a-select v-model="formData.type" placeholder="请选择申请类型" :disabled="readOnlyMode" @change="handleTypeChange">
                                    <a-option :value="1">减免</a-option>
                                    <a-option :value="2">缓缴</a-option>
                                    <a-option :value="3">分期</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="reason" label="申请原因" required>
                                <a-select v-model="formData.reason" placeholder="请选择申请原因" :disabled="readOnlyMode">
                                    <a-option v-for="option in reasonOptions" :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <!-- 申请说明 -->
                    <a-row :gutter="16">
                        <a-col :span="24">
                            <a-form-item field="remark" label="申请说明">
                                <a-textarea
                                    v-model="formData.remark"
                                    placeholder="请输入申请说明"
                                    :max-length="200"
                                    :disabled="readOnlyMode"
                                    show-word-limit
                                    :auto-size="{ minRows: 2, maxRows: 4 }"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>

                <!-- 调整信息 -->
                <div class="form-section">
                    <SectionTitle title="调整信息" style="margin-bottom: 16px;" />
                    <a-row :gutter="16" v-if="!readOnlyMode">
                        <a-col :span="6">
                            <a-form-item field="costType" label="费用类型">
                                <a-select v-model="queryForm.costType" placeholder="请选择费用类型" :disabled="readOnlyMode">
                                    <a-option :value="2">租金</a-option>
                                    <a-option :value="3">其他费用</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="startDate" label="应收开始日期">
                                <a-date-picker
                                    v-model="queryForm.startDate"
                                    style="width: 100%"
                                    :disabled="readOnlyMode"
                                    format="YYYY-MM-DD"
                                    :disabled-date="(date: Date) => queryForm.endDate && date > new Date(queryForm.endDate)"
                                    @change="handleStartDateChange"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-item field="endDate" label="应收结束日期">
                                <a-date-picker
                                    v-model="queryForm.endDate"
                                    style="width: 100%"
                                    :disabled="readOnlyMode"
                                    format="YYYY-MM-DD"
                                    :disabled-date="(date: Date) => queryForm.startDate && date < new Date(queryForm.startDate)"
                                    @change="handleEndDateChange"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="6" v-if="!readOnlyMode">
                            <a-button type="primary" @click="handleQueryCosts">
                                查询
                            </a-button>
                        </a-col>
                    </a-row>
                </div>

                <!-- 调整明细 -->
                <div class="form-section table">
                    <a-table
                        :columns="detailColumns"
                        :data="formData.adjustList"
                        :row-key="(record: any) => record.tempId"
                        :pagination="{
                            pageSize: 10,
                            showTotal: true,
                            showPageSize: false,
                            hideOnSinglePage: true
                        }"
                        :bordered="{ cell: true }"
                        size="small"
                        :scroll="{ x: 1400 }"
                    >
                        <template #costType="{ record }">
                            <span>{{ getCostTypeText(record.costType) }}</span>
                        </template>
                        <template #rentPeriod="{ record }">
                            <span>{{ record.startDate && record.endDate ? `${record.startDate} 至 ${record.endDate}` : "" }}</span>
                        </template>
                        <template #receivableDate="{ record }">
                            <span>{{ record.receivableDate }}</span>
                        </template>
                        <template #totalAmount="{ record }">
                            <span>{{ record.totalAmount }}</span>
                        </template>
                        <template #discountAmount="{ record }">
                            <span>{{ floatCalculate.toFixed(floatCalculate.add(record.discountAmount || 0, record.originalReductionAmount || 0)) }}</span>
                        </template>
                        <template #actualReceivable="{ record }">
                            <span>{{ record.actualReceivable }}</span>
                        </template>
                        <template #receivedAmount="{ record }">
                            <span>{{ record.receivedAmount }}</span>
                        </template>
                        <template #pendingAmount="{ record }">
                            <span>{{ floatCalculate.toFixed(record.pendingAmount || 0) }}</span>
                        </template>
                        <!-- 减免金额 - 仅减免类型显示 -->
                        <template #reductionAmount="{ record }" v-if="formData.type === 1">
                            <span v-if="readOnlyMode">{{ record.reductionAmount }}</span>
                            <a-input-number
                                v-else
                                v-model="record.reductionAmount"
                                :precision="2"
                                :min="0"
                                :max="record.pendingAmount || 0"
                                placeholder="不能大于未收金额"
                                @change="handleReductionAmountChange(record)"
                            />
                        </template>
                        <!-- 调整后实际应收 - 仅减免类型显示 -->
                        <template #currActualReceivable="{ record }" v-if="formData.type === 1">
                            <span>{{ floatCalculate.toFixed(record.currActualReceivable || 0) }}</span>
                        </template>
                        <!-- 缓缴日期 - 仅缓缴类型显示 -->
                        <template #delayTime="{ record }" v-if="formData.type === 2">
                            <span v-if="readOnlyMode">{{ record.delayTime }}</span>
                            <a-date-picker
                                v-else
                                v-model="record.delayTime"
                                style="width: 100%"
                                format="YYYY-MM-DD"
                                :disabled-date="(date: Date) => record.receivableDate && date <= new Date(record.receivableDate)"
                                placeholder="必须大于应收日期"
                            />
                        </template>
                        <!-- 拆分操作 - 仅分期类型显示，且未收过款的账单才能拆分 -->
                        <template #split="{ record, rowIndex }" v-if="formData.type === 3">
                            <a-button
                                type="text"
                                size="mini"
                                @click="handleSplitCost(record, rowIndex)"
                                :disabled="readOnlyMode || !canSplit(record)"
                            >
                                拆分
                            </a-button>
                        </template>
                        <!-- 通用操作 -->
                        <template #action="{ rowIndex }" v-if="!readOnlyMode && formData.type !== 3">
                            <a-button type="text" size="mini" status="danger" @click="handleRemoveDetail(rowIndex)">
                                删除
                            </a-button>
                        </template>
                    </a-table>
                </div>

                <!-- 附件 -->
                <div class="form-section">
                    <SectionTitle title="附件" style="margin-bottom: 16px;" />
                    <uploadFile 
                        v-model="formData.attachments" 
                        :readonly="readOnlyMode"
                        :limit="10"
                        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                    />
                </div>
            </a-form>
        </div>

        <!-- 拆分抽屉 -->
        <SplitDrawer
            v-if="showSplitDrawer"
            ref="splitDrawerRef"
            @success="handleSplitSuccess"
            @cancel="handleSplitCancel"
        />
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
    addReductionOnly,
    editReduction,
    addPostponed,
    editPostponed,
    addSplit,
    editSplit,
    getReductionDetail,
    getSplitDetail,
    getUnpaidCost,
    getContractList,
    getCustomerRooms
} from '@/api/reduction'
import customerApi, { type CustomerQueryDTO } from '@/api/customer'
import ProjectTreeSelect from '@/components/projectTreeSelect/index.vue'
import SectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import SplitDrawer from './SplitDrawer.vue'
import { IconPlus } from '@arco-design/web-vue/es/icon'
import { DictType, getDictLabel } from '@/dict/index'
import { useDictSync } from '@/utils/dict'

// 组件事件
const emit = defineEmits<{
    success: []
    cancel: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const readOnlyMode = ref(false)
const isEdit = ref(false)
const isInitialLoad = ref(false) // 标记是否是初次加载数据
const formRef = ref()
const splitDrawerRef = ref()
const showSplitDrawer = ref(false)

// 表单数据
const formData = reactive<any>({
    id: '',
    type: 1,
    projectId: '',
    projectName: '',
    customerId: '',
    contractId: '',
    contractNo: '',
    unionId: '', // 合同统一ID
    startDate: '',
    endDate: '',
    roomId: '',
    customerName: '',
    roomName: '',
    contractPeriod: '',
    contractPurpose: '',
    contractPurposeText: '',
    reason: '',
    remark: '',
    adjustList: [],
    attachments: []
})

// 查询表单数据
const queryForm = reactive({
    costType: undefined,
    startDate: '',
    endDate: ''
})

// 选项数据
const contractOptions = ref<any[]>([])
const customerOptions = ref<any[]>([])
const roomOptions = ref<any[]>([])

// 字典数据
const reasonOptions = ref<any[]>([])
const contractPurposeOptions = ref<any[]>([])

// 计算属性
const drawerTitle = computed(() => {
    if (readOnlyMode.value) {
        return getTypeText(formData.type) + '详情'
    }
    return isEdit.value ? '编辑' + getTypeText(formData.type) : '新增' + getTypeText(formData.type)
})

// 表单验证规则
const formRules = {
    type: [{ required: true, message: '请选择申请类型' }],
    projectId: [{ required: true, message: '请选择项目' }],
    customerId: [{ required: true, message: '请选择承租人' }],
    contractId: [{ required: true, message: '请选择合同' }],
    reason: [{ required: true, message: '请选择申请原因' }]
}

// 调整明细表格列
const detailColumns = computed(() => {
    const baseColumns: any[] = [
        {
            title: '账单类型',
            dataIndex: 'costType',
            slotName: 'costType',
            width: 120,
            align: 'center'
        },
        {
            title: '账单周期',
            dataIndex: 'rentPeriod',
            slotName: 'rentPeriod',
            width: 150,
            align: 'center'
        },
        {
            title: '应收日期',
            dataIndex: 'receivableDate',
            slotName: 'receivableDate',
            width: 120,
            align: 'center'
        },
        {
            title: '账单总额（元）',
            dataIndex: 'totalAmount',
            slotName: 'totalAmount',
            width: 120,
            align: 'center'
        },
        {
            title: '优惠金额（元）',
            dataIndex: 'discountAmount',
            slotName: 'discountAmount',
            width: 120,
            align: 'center'
        },
        {
            title: '实际应收（元）',
            dataIndex: 'actualReceivable',
            slotName: 'actualReceivable',
            width: 120,
            align: 'center'
        }
    ]

    // 根据申请类型添加不同的列
    if (formData.type === 1) {
        // 减免：添加实际已收、实际未收、减免金额、调整后实际应收
        baseColumns.push(
            {
                title: '实际已收（元）',
                dataIndex: 'receivedAmount',
                slotName: 'receivedAmount',
                width: 120,
                align: 'center'
            },
            {
                title: '实际未收（元）',
                dataIndex: 'pendingAmount',
                slotName: 'pendingAmount',
                width: 120,
                align: 'center'
            },
            {
                title: '减免金额（元）',
                dataIndex: 'reductionAmount',
                slotName: 'reductionAmount',
                width: 150,
                align: 'center'
            },
            {
                title: '调整后实际应收（元）',
                dataIndex: 'currActualReceivable',
                slotName: 'currActualReceivable',
                width: 140,
                align: 'center'
            }
        )
    } else if (formData.type === 2) {
        // 缓缴：添加实际已收、实际未收、缓缴日期
        baseColumns.push(
            {
                title: '实际已收（元）',
                dataIndex: 'receivedAmount',
                slotName: 'receivedAmount',
                width: 120,
                align: 'center'
            },
            {
                title: '实际未收（元）',
                dataIndex: 'pendingAmount',
                slotName: 'pendingAmount',
                width: 120,
                align: 'center'
            },
            {
                title: '缓缴日期',
                dataIndex: 'delayTime',
                slotName: 'delayTime',
                width: 140,
                align: 'center'
            }
        )
    } else if (formData.type === 3 && !readOnlyMode.value) {
        // 分期：只添加操作（拆分）
        baseColumns.push({
            title: '操作',
            dataIndex: 'split',
            slotName: 'split',
            width: 80,
            align: 'center'
        })
    }
    return baseColumns
})

// 获取类型文本
const getTypeText = (type: number) => {
    const typeMap: Record<number, string> = {
        1: '减免',
        2: '缓缴',
        3: '分期'
    }
    return typeMap[type] || ''
}

// 获取费用类型文本
const getCostTypeText = (costType: number) => {
    const typeMap: Record<number, string> = {
        1: '保证金',
        2: '租金',
        3: '其他费用'
    }
    return typeMap[costType] || ''
}

// 获取合同状态文本
const getContractStatusText = (status: number) => {
    return getDictLabel(DictType.CONTRACT_STATUS, status) || '未知'
}

// 获取合同用途文本
const getContractPurposeText = (purpose: number) => {
    if (!purpose) return '未知'
    return getDictLabel(DictType.DIVERGENCE_PURPOSE, purpose) || '未知'
}

// 初始化字典数据
const initDictionaries = async () => {
    try {
        const dictData = await useDictSync('reduction_postponed_reason', 'diversification_purpose')

        // 处理申请原因字典
        if (dictData.reduction_postponed_reason) {
            reasonOptions.value = dictData.reduction_postponed_reason.map((item: any) => ({
                label: item.label,
                value: item.value
            }))
        }

        // 处理合同用途字典
        if (dictData.diversification_purpose) {
            contractPurposeOptions.value = dictData.diversification_purpose.map((item: any) => ({
                label: item.label,
                value: item.value
            }))
        }
    } catch (error) {
        console.error('获取字典数据失败:', error)
    }
}

// 计算调整后实际应收
const calculateActualReceivable = (record: any) => {
    if (formData.type === 1) {
        // 减免：实际应收 - 减免金额，使用浮点数安全计算
        const actualReceivable = record.actualReceivable || 0
        const reductionAmount = record.reductionAmount || 0
        record.currActualReceivable = floatCalculate.subtract(actualReceivable, reductionAmount)
    }
}

// 处理减免金额变化
const handleReductionAmountChange = (record: any) => {
    const reductionAmount = record.reductionAmount || 0
    const pendingAmount = record.pendingAmount || 0

    // 验证减免金额不能大于实际未收
    if (reductionAmount > pendingAmount) {
        record.reductionAmount = pendingAmount
        Message.warning('减免金额不能大于实际未收金额')
    }

    // 重新计算调整后实际应收
    calculateActualReceivable(record)
}

// 判断账单是否可以拆分
const canSplit = (record: any) => {
    // 只有未收过款的账单才能拆分
    // 如果实际已收金额大于0，说明已经收过款，不允许拆分
    const receivedAmount = record.receivedAmount || 0
    return receivedAmount === 0
}

// 拆分费用
const handleSplitCost = async (record: any, rowIndex: number) => {
    // 再次检查是否可以拆分
    if (!canSplit(record)) {
        Message.warning('已收款的账单不允许拆分')
        return
    }

    showSplitDrawer.value = true
    await nextTick()

    if (!splitDrawerRef.value) return

    // 打开拆分抽屉，传入账单数据
    splitDrawerRef.value.show(record)
}

// 拆分成功回调
const handleSplitSuccess = (splitData?: any) => {
    showSplitDrawer.value = false

    if (splitData && splitData.originalTempId && splitData.splitList) {
        // 根据tempId找到原账单信息
        const originalIndex = formData.adjustList.findIndex((item: any) =>
            item.tempId === splitData.originalTempId
        )

        if (originalIndex !== -1) {
            // 移除原账单
            formData.adjustList.splice(originalIndex, 1)

            // 添加拆分后的账单列表（多条相同costId的记录）
            splitData.splitList.forEach((splitItem: any, index: number) => {
                const newItem = {
                    costId: splitItem.costId, // 保持相同的costId
                    tempId: splitItem.tempId, // 拆分后的临时ID
                    costType: splitItem.costType,
                    period: splitItem.period,
                    rentPeriod: splitItem.rentPeriod,
                    startDate: splitItem.startDate,
                    endDate: splitItem.endDate,
                    receivableDate: splitItem.receivableDate,
                    totalAmount: splitItem.totalAmount || 0,
                    discountAmount: splitItem.discountAmount || 0,
                    originalReductionAmount: splitItem.originalReductionAmount || 0, // 原减免金额
                    actualReceivable: splitItem.actualReceivable || 0,
                    receivedAmount: splitItem.receivedAmount || 0,
                    pendingAmount: floatCalculate.subtract(splitItem.actualReceivable || 0, splitItem.receivedAmount || 0),
                    reductionAmount: 0,
                    delayTime: '',
                    currActualReceivable: floatCalculate.toFixed(splitItem.actualReceivable || 0),
                    isSplit: splitItem.isSplit // 标记为拆分后的账单
                }

                // 插入到原位置
                formData.adjustList.splice(originalIndex + index, 0, newItem)
            })

            Message.success(`账单拆分成功，已拆分为${splitData.splitList.length}条记录`)
        } else {
            Message.warning('未找到原账单，将重新查询账单列表')
            handleQueryCosts()
        }
    } else {
        // 如果没有返回拆分数据，则重新查询费用账单
        handleQueryCosts()
    }
}

// 拆分取消回调
const handleSplitCancel = () => {
    showSplitDrawer.value = false
}

// 处理类型变化
const handleTypeChange = (value: number) => {
    formData.adjustType = getTypeText(value)

    // 编辑模式下，如果合同ID有值，需要重新调用账单接口
    if (isEdit.value && formData.contractId && !readOnlyMode.value) {
        // 清空之前的调整明细
        formData.adjustList = []
        // 重新查询费用账单
        handleQueryCosts()
    }
}

// 处理开始日期变化
const handleStartDateChange = (value: string) => {
    // 如果开始日期大于结束日期，清空结束日期
    if (value && queryForm.endDate && new Date(value) > new Date(queryForm.endDate)) {
        queryForm.endDate = ''
    }
}

// 处理结束日期变化
const handleEndDateChange = (value: string) => {
    // 如果结束日期小于开始日期，清空开始日期
    if (value && queryForm.startDate && new Date(value) < new Date(queryForm.startDate)) {
        queryForm.startDate = ''
    }
}

// 处理项目变化（仅在编辑模式下可能触发）
const handleProjectChange = (value: string, option?: any) => {
    // 先设置项目ID
    formData.projectId = value
    // 设置项目名称（如果组件提供了option信息）
    if (option && option.name) {
        formData.projectName = option.name
    }

    // 清空相关数据
    formData.customerId = ''
    formData.customerName = ''
    formData.contractId = ''
    formData.contractNo = ''
    formData.unionId = '' // 清空合同统一ID
    formData.startDate = ''
    formData.endDate = ''
    formData.roomId = ''
    formData.roomName = ''
    formData.contractPeriod = ''
    formData.contractPurpose = ''
    formData.contractPurposeText = ''
    // 清空调整明细
    formData.adjustList = []
    // 清空选项（房间选项也要清空，因为房间数据根据承租人来）
    customerOptions.value = []
    contractOptions.value = []
    roomOptions.value = []

    // 判断是否需要加载项目数据
    if (value) {
        let shouldLoadData = false

        if (!isEdit.value && !readOnlyMode.value) {
            // 新增模式：总是需要加载
            shouldLoadData = true
        } else if (isEdit.value && !readOnlyMode.value) {
            // 编辑模式：只有非初次加载时才需要加载（用户手动更改项目时）
            shouldLoadData = !isInitialLoad.value
        } else if (readOnlyMode.value) {
            // 查看模式：永远不需要加载（只读模式）
            shouldLoadData = false
        }

        if (shouldLoadData) {
            // 根据项目id加载承租人和合同数据，不加载房间数据
            loadProjectData(value)
        }
    }
}

// 处理承租人变化
const handleCustomerChange = (value: string) => {
    const customer = customerOptions.value.find(item => item.id === value)
    if (customer) {
        formData.customerName = customer.customerName
        // 清空合同选择，因为要重新根据承租人筛选
        formData.contractId = ''
        formData.unionId = '' // 清空合同统一ID
        formData.roomName = ''
        formData.contractPeriod = ''
        formData.contractPurpose = ''
        formData.contractPurposeText = ''
        // 清空房间选择，因为房间数据根据承租人来
        formData.roomId = ''
        // 清空调整明细
        formData.adjustList = []

        // 根据承租人名称重新获取合同以及房间号
        Promise.all([
            loadContracts({
                projectId: formData.projectId,
                customerName: customer.customerName
            }),
            loadRooms(customer.customerName) // 加载该承租人的房间列表
        ])
    }
}

// 处理合同变化
const handleContractChange = (value: string) => {
    const contract = contractOptions.value.find(item => item.id === value)
    if (contract) {
        // 回填承租人名称和承租人id
        formData.customerId = contract.customerId
        formData.customerName = contract.customerName
        formData.contractNo = contract.contractNo
        formData.unionId = contract.unionId // 设置合同统一ID
        formData.roomName = contract.roomName
        formData.contractPeriod = contract.contractPeriod
        formData.contractPurpose = contract.contractPurpose
        formData.contractPurposeText = getContractPurposeText(contract.contractPurpose)

        // 设置合同开始和结束日期
        formData.startDate = contract.startDate
        formData.endDate = contract.endDate

        // 清空之前的调整明细
        formData.adjustList = []

        loadRooms(formData.customerName) // 加载该承租人的房间列表
    }
}

// 处理房间变化
const handleRoomChange = async (value: string) => {
    const room = roomOptions.value.find(item => item.id === value)
    if (room && formData.projectId) {
        // 保存当前选择的合同ID
        const currentContractId = formData.contractId

        // 根据房间ID筛选合同列表，同时保留承租人姓名筛选条件（并集）
        const queryParams: Record<string, any> = {
            projectId: formData.projectId,
            roomId: value
        }

        // 如果已选择承租人，同时传递承租人姓名进行并集筛选
        if (formData.customerName) {
            queryParams.customerName = formData.customerName
        }

        await loadContracts(queryParams)

        // 检查当前选择的合同是否在新的合同列表中
        const contractExists = contractOptions.value.some(contract => contract.id === currentContractId)

        if (!contractExists && currentContractId) {
            // 如果当前选择的合同不在筛选后的合同列表中，清空合同相关信息
            // 但保留承租人信息，因为承租人是独立选择的
            formData.contractId = ''
            formData.unionId = '' // 清空合同统一ID
            formData.roomName = ''
            formData.contractPeriod = ''
            formData.contractPurpose = ''
            formData.contractPurposeText = ''
            console.log('当前选择的合同不在该房间的合同列表中，已清空合同选择')
        }

        // 清空之前的调整明细
        formData.adjustList = []
    }
}

// 查询费用账单
const handleQueryCosts = async () => {
    if (!formData.contractId) {
        Message.warning('请先选择合同')
        return
    }

    try {
        loading.value = true

        // 调用API查询未收齐账单
        const response = await getUnpaidCost({
            projectId: formData.projectId,
            contractId: formData.contractId,
            costType: queryForm.costType,
            startDate: queryForm.startDate,
            endDate: queryForm.endDate,
            type: formData.type
        })

        // 处理返回的账单数据
        if (response && response.data) {
            formData.adjustList = response.data.map((item: any, index: number) => {
                const actualReceivable = item.actualReceivable || 0
                const receivedAmount = item.receivedAmount || 0
                // 计算实际未收：实际应收 - 实际已收
                const pendingAmount = floatCalculate.subtract(actualReceivable, receivedAmount)

                return {
                    costId: item.costId,
                    tempId: `${item.costId}_${index}`, // 生成临时ID：costId + 索引
                    costType: item.costType,
                    period: item.period,
                    rentPeriod: item.rentPeriod,
                    startDate: item.startDate,
                    endDate: item.endDate,
                    receivableDate: item.receivableDate,
                    totalAmount: item.totalAmount || 0,
                    discountAmount: item.discountAmount || 0,
                    originalReductionAmount: item.originalReductionAmount || 0, // 原减免金额
                    actualReceivable: actualReceivable,
                    receivedAmount: receivedAmount,
                    pendingAmount: pendingAmount, // 通过计算得出：实际应收 - 实际已收
                    reductionAmount: 0, // 初始化减免金额
                    delayTime: '', // 初始化缓缴日期
                    currActualReceivable: floatCalculate.toFixed(actualReceivable) // 初始化调整后实际应收（计算字段需要处理）
                }
            })
        } else {
            formData.adjustList = []
            Message.info('未查询到符合条件的账单')
        }
    } catch (error) {
        console.error('查询费用账单失败:', error)
        formData.adjustList = []
    } finally {
        loading.value = false
    }
}

// 加载项目相关数据（承租人列表、合同列表）
const loadProjectData = async (projectId: string) => {
    if (!projectId) return

    try {
        loading.value = true

        // 根据项目id并行加载承租人列表、合同列表
        // 房间数据根据承租人来，不在初始化时加载
        await Promise.all([
            loadContracts({ projectId }), // 直接传入projectId参数
            loadCustomersByProject(projectId)
        ])

    } catch (error) {
        console.error('加载项目数据失败:', error)
    } finally {
        loading.value = false
    }
}

// 统一的合同加载方法
const loadContracts = async (additionalParams: Record<string, any> = {}) => {
    // 优先使用传入的projectId参数，如果没有则使用formData.projectId
    const projectId = additionalParams.projectId || formData.projectId
    if (!projectId) return

    try {
        // 构建查询参数 - 只包含API支持的4个参数
        const queryParams: {
            projectId?: string
            contractNo?: string
            customerName?: string
            roomId?: string
        } = {
            projectId // 必须参数：项目ID
        }

        // 合并传入的额外参数（只接受API支持的4个参数）
        const allowedParams = ['contractNo', 'customerName', 'roomId'] // 不包含projectId，因为已经处理过了
        allowedParams.forEach(key => {
            if (additionalParams[key] !== undefined && additionalParams[key] !== '') {
                queryParams[key as keyof typeof queryParams] = additionalParams[key]
            }
        })

        // 调用API获取合同列表
        const response = await getContractList(queryParams)

        if (response && response.data) {
            contractOptions.value = response.data.map((item: any) => ({
                id: item.id,
                contractId: item.id,
                contractNo: item.contractNo,
                unionId: item.unionId, // 添加合同统一ID
                status: item.status,
                statusText: getContractStatusText(item.status),
                customerName: item.customerName,
                customerId: item.customerId,
                roomName: item.roomName,
                roomId: item.roomId,
                contractPeriod: `${item.startDate} 至 ${item.endDate}`,
                contractPurpose: item.contractPurpose,
                contractPurposeText: getContractPurposeText(item.contractPurpose),
                startDate: item.startDate,
                endDate: item.endDate
            }))
        } else {
            contractOptions.value = []
        }

    } catch (error) {
        console.error('加载合同列表失败:', error)
        contractOptions.value = []
    }
}

// 根据项目加载客户列表
const loadCustomersByProject = async (projectId: string) => {
    if (!projectId) return

    try {
        // 调用客户管理API获取客户列表
        const params: CustomerQueryDTO = {
            pageNum: 1,
            pageSize: 10000, // 获取足够多的数据
            projectId: projectId
        }
        const response = await customerApi.getCustomerList(params)

        if (response && response.rows) {
            // 处理返回的客户数据 - 根据客户列表页面的处理方式
            const customers = response.rows || []
            customerOptions.value = customers.map((item: any) => ({
                id: item.id,
                customerId: item.id,
                customerName: item.customerName,
                customerPhone: item.contactPhone,
                customerType: item.customerType
            }))
        } else {
            customerOptions.value = []
        }
    } catch (error) {
        console.error('加载客户列表失败:', error)
        customerOptions.value = []
    }
}

// 加载房间列表（传项目id是固定的，传承租人姓名是为了进一步筛选）
const loadRooms = async (customerName?: string, projectId?: string) => {
    const targetProjectId = projectId || formData.projectId
    if (!targetProjectId) return

    try {
        // 调用API获取房间列表，项目id固定传递，承租人姓名用于筛选
        const response = await getCustomerRooms({
            customerId: '', // 不传customerId
            customerName: customerName || '', // 承租人姓名用于筛选，可选
            projectId: targetProjectId // 项目id固定传递
        })

        if (response && response.data) {
            roomOptions.value = response.data.map((item: any) => ({
                id: item.roomId,
                roomId: item.roomId,
                roomName: item.roomName,
                projectId: item.projectId,
                projectName: item.projectName,
                parcelName: item.parcelName,
                buildingName: item.buildingName,
                floorName: item.floorName,
                propertyType: item.propertyType,
                rentArea: item.rentArea
            }))
        } else {
            roomOptions.value = []
        }
    } catch (error) {
        console.error('加载房间列表失败:', error)
        roomOptions.value = []
    }
}





// 删除调整明细
const handleRemoveDetail = (index: number) => {
    formData.adjustList.splice(index, 1)
}

// 清理提交数据，只保留接口需要的字段
const cleanSubmitData = (data: any) => {
    // 清理主表单数据
    const cleanedData: any = {
        id: data.id,
        type: data.type,
        projectId: data.projectId,
        projectName: data.projectName,
        customerId: data.customerId,
        customerName: data.customerName,
        contractId: data.contractId,
        contractNo: data.contractNo,
        unionId: data.unionId, // 添加合同统一ID
        startDate: data.startDate,
        endDate: data.endDate,
        roomName: data.roomName,
        contractPurpose: data.contractPurpose,
        reason: data.reason,
        remark: data.remark,
        status: data.status,
        attachments: (data.attachments && data.attachments.length > 0) ? data.attachments : null,
        adjustList: []
    }

    // 清理调整明细数据
    if (data.adjustList && data.adjustList.length > 0) {
        cleanedData.adjustList = data.adjustList.map((item: any) => {
            const cleanedItem: any = {
                contractId: data.contractId, // 合同ID
                costId: item.costId,
                costType: item.costType,
                period: item.period,
                startDate: item.startDate,
                endDate: item.endDate,
                receivableDate: item.receivableDate,
                totalAmount: item.totalAmount,
                discountAmount: item.discountAmount,
                originalReductionAmount: item.originalReductionAmount, // 原减免金额
                actualReceivable: item.actualReceivable,
                receivedAmount: item.receivedAmount,
                pendingAmount: item.pendingAmount // 实际未收
                // 注意：这里不包含tempId，提交时去掉临时ID
            }

            // 根据类型添加特定字段
            if (data.type === 1) {
                // 减免类型：添加减免金额和调整后实际应收
                cleanedItem.reductionAmount = floatCalculate.toFixed(item.reductionAmount || 0)
                cleanedItem.currActualReceivable = floatCalculate.toFixed(item.currActualReceivable || 0)
            } else if (data.type === 2) {
                // 缓缴类型：添加缓缴日期
                cleanedItem.delayTime = item.delayTime
            }
            // 分期类型不需要额外字段

            return cleanedItem
        })
    }

    return cleanedData
}

// 根据类型调用新增接口
const callAddApi = async (data: any) => {
    switch (data.type) {
        case 1: // 减免
            return await addReductionOnly(data)
        case 2: // 缓缴
            return await addPostponed(data)
        case 3: // 分期
            return await addSplit(data)
        default:
            throw new Error('未知的类型')
    }
}

// 根据类型调用编辑接口
const callEditApi = async (data: any) => {
    switch (data.type) {
        case 1: // 减免
            return await editReduction(data)
        case 2: // 缓缴
            return await editPostponed(data)
        case 3: // 分期
            return await editSplit(data)
        default:
            throw new Error('未知的类型')
    }
}

// 提交表单
const handleSubmit = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return

        loading.value = true

        // 清理数据并设置状态
        const submitData = cleanSubmitData({ ...formData, status: 1 })

        if (isEdit.value) {
            await callEditApi(submitData)
            Message.success('更新成功')
        } else {
            await callAddApi(submitData)
            Message.success('提交成功')
        }

        visible.value = false
        emit('success')
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        loading.value = false
    }
}

// 暂存
const handleSave = async () => {
    try {
        const errors = await formRef.value?.validate()
        if (errors) return
        loading.value = true

        // 清理数据并设置状态
        const saveData = cleanSubmitData({ ...formData, status: 0 })

        if (isEdit.value) {
            await callEditApi(saveData)
            Message.success('暂存成功')
        } else {
            await callAddApi(saveData)
            Message.success('暂存成功')
        }

        visible.value = false
        emit('success')
    } catch (error) {
        console.error('暂存失败:', error)
    } finally {
        loading.value = false
    }
}

// 加载详情数据
const loadDetailData = async (id: string, type: number) => {
    try {
        loading.value = true
        isInitialLoad.value = true // 标记为初次加载
        let response: any

        if (type === 3) {
            // 分期类型：调用分期详情接口
            response = await getSplitDetail(id)
        } else {
            // 减免和缓缴类型：调用减免缓缴详情接口
            response = await getReductionDetail(id)
        }

        if (response && response.data) {
            // 清空表单数据
            Object.keys(formData).forEach(key => {
                if (Array.isArray(formData[key])) {
                    formData[key] = []
                } else {
                    formData[key] = ''
                }
            })

            // 设置基本信息数据（从 reductionInfo 中获取）
            if (response.data.reductionInfo) {
                const reductionInfo = response.data.reductionInfo
                Object.assign(formData, reductionInfo)

                // 处理合同周期显示（基于开始和结束日期）
                if (formData.startDate && formData.endDate) {
                    formData.contractPeriod = `${formData.startDate} 至 ${formData.endDate}`
                }

                // 处理合同用途显示文本
                if (formData.contractPurpose) {
                    formData.contractPurposeText = getContractPurposeText(formData.contractPurpose)
                }

                // 使用详情接口返回的项目ID加载项目相关数据（而不是列表传递的项目ID）
                if (reductionInfo.projectId) {
                    // 加载客户列表
                    await loadCustomersByProject(reductionInfo.projectId)

                    // 如果有客户名称，根据客户名称加载合同和房间列表
                    if (reductionInfo.customerName) {
                        await Promise.all([
                            loadContracts({
                                projectId: reductionInfo.projectId,
                                customerName: reductionInfo.customerName
                            }),
                            loadRooms(reductionInfo.customerName, reductionInfo.projectId)
                        ])
                    }
                }
            }

            // 设置调整明细数据
            if (response.data.adjustList) {
                formData.adjustList = response.data.adjustList.map((item: any, index: number) => {
                    const actualReceivable = item.actualReceivable || 0
                    const receivedAmount = item.receivedAmount || 0
                    // 计算实际未收：实际应收 - 实际已收
                    const pendingAmount = floatCalculate.subtract(actualReceivable, receivedAmount)

                    return {
                        ...item,
                        tempId: item.tempId || `${item.costId}_${index}${item.isSplit ? '_split' : ''}`, // 生成临时ID，如果是拆分的加上_split标识
                        originalReductionAmount: item.originalReductionAmount || 0, // 确保原减免金额字段存在
                        pendingAmount: pendingAmount // 通过计算得出：实际应收 - 实际已收
                    }
                })
            }
        }
    } catch (error) {
        console.error('加载详情数据失败:', error)
        Message.error('加载数据失败')
    } finally {
        loading.value = false
        isInitialLoad.value = false // 重置初次加载标记
    }
}

// 显示弹框
const show = async (data?: any, mode: 'add' | 'edit' | 'view' = 'add') => {
    visible.value = true
    readOnlyMode.value = mode === 'view'
    isEdit.value = mode === 'edit'
    isInitialLoad.value = false // 重置初次加载标记

    if (data) {
        if (mode === 'add') {
            // 新增模式：直接使用传入的数据
            Object.assign(formData, data)

            // 如果有项目ID，自动加载项目相关数据
            if (data.projectId) {
                await loadProjectData(data.projectId)
            }
        } else {
            // 编辑和查看模式：调用详情接口获取完整数据
            await loadDetailData(data.id, data.type)
        }
    }
}

// 取消
const handleCancel = () => {
    visible.value = false
    setTimeout(() => {
        emit('cancel')
    }, 200);
}

// 浮点数运算工具函数
const floatCalculate = {
    // 加法
    add: (a: number, b: number): number => {
        const factor = Math.pow(10, 2) // 保留2位小数
        return Math.round((a + b) * factor) / factor
    },
    // 减法
    subtract: (a: number, b: number): number => {
        const factor = Math.pow(10, 2) // 保留2位小数
        return Math.round((a - b) * factor) / factor
    },
    // 乘法
    multiply: (a: number, b: number): number => {
        const factor = Math.pow(10, 2) // 保留2位小数
        return Math.round((a * b) * factor) / factor
    },
    // 除法
    divide: (a: number, b: number): number => {
        if (b === 0) return 0
        const factor = Math.pow(10, 2) // 保留2位小数
        return Math.round((a / b) * factor) / factor
    },
    // 格式化为2位小数
    toFixed: (num: number): number => {
        return Math.round(num * 100) / 100
    }
}

// 初始化字典数据
initDictionaries()

// 暴露方法
defineExpose({
    show
})
</script>

<style scoped lang="less">

.list-header {
    margin: 16px 0;
}

.list-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}
.form-section {
    &.table{
        padding: 0 16px;
        margin-bottom: 16px;
    }
}
</style>
