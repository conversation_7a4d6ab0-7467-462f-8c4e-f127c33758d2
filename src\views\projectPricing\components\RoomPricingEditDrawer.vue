<template>
  <a-drawer v-model:visible="visible" title="房源定价信息编辑" class="common-drawer-small" @cancel="handleCancel">
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleConfirm" :loading="loading">确认</a-button>
    </template>

    <a-form ref="formRef" :model="formData" :rules="formRules" label-align="right" layout="horizontal"
      :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>
      <!-- 房源基本信息 -->
      <a-row>
        <a-col :span="12">
          <a-form-item label="房源名称" field="roomName">
            <a-input v-model="formData.roomName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="地块" field="plotName">
            <a-select v-model="formData.plotName" disabled>
              <a-option :value="formData.plotName">{{ formData.plotName }}</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="楼栋" field="buildingName">
            <a-select v-model="formData.buildingName" disabled>
              <a-option :value="formData.buildingName">{{ formData.buildingName }}</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="楼层" field="floorName">
            <a-select v-model="formData.floorName" disabled>
              <a-option :value="formData.floorName">{{ formData.floorName }}</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="用途" field="roomUsage">
            <a-input v-model="displayRoomUsage" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="isDormitory">
          <a-form-item label="户型" field="roomType">
            <a-input v-model="formData.roomType" disabled />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 定价信息 -->
      <SectionTitle title="定价" style="margin-bottom: 16px;" />
      <a-row>
        <a-col :span="12" v-if="isShopOrComplex">
          <a-form-item label="规划业态" field="planningBusiness">
            <a-select v-model="formData.planningBusiness" placeholder="请选择">
              <a-option value="超市">超市</a-option>
              <a-option value="零售集合">零售集合</a-option>
              <a-option value="医疗健康">医疗健康</a-option>
              <a-option value="文化教育">文化教育</a-option>
              <a-option value="娱乐休闲">娱乐休闲</a-option>
              <a-option value="服装">服装</a-option>
              <a-option value="服饰配套">服饰配套</a-option>
              <a-option value="餐饮">餐饮</a-option>
              <a-option value="生活配套">生活配套</a-option>
              <a-option value="产业配套">产业配套</a-option>
              <a-option value="儿童">儿童</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="基础租金" field="baseRent">
            <a-input-number v-model="formData.baseRent" placeholder="请输入" :precision="2" :min="0" style="width: 100%">
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="附加费用" field="additionalFee">
            <a-input-number v-model="formData.additionalFee" placeholder="请输入" :precision="2" :min="0"
              style="width: 100%">
            </a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="计租单位" field="calcUnit">
            <a-select v-model="formData.calcUnit" placeholder="请选择">
              <a-option :value="1">元/平方米/月</a-option>
              <a-option :value="2">元/月</a-option>
              <a-option :value="3">元/日</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-form-item label="租金是否递增" field="isRentIncrease">
            <a-radio-group v-model="formData.isRentIncrease">
              <a-radio :value="0">否</a-radio>
              <a-radio :value="1">是</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row v-if="formData.isRentIncrease === 1">
        <a-col :span="12">
          <a-form-item label="递增间隔" field="increaseInterval">
            <a-input-number v-model="formData.increaseInterval" placeholder="请输入" :min="1" style="width: 100%">
              <template #append>年</template>
            </a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单价递增率" field="increaseRate">
            <a-input-number v-model="formData.increaseRate" placeholder="请输入" :precision="2" :min="0" :max="100"
              style="width: 100%">
              <template #append>%</template>
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="保证金" required>
            <a-input-group style="width: 100%;">
              <div :style="{ width: formData.depositType === 0 ? '40%' : '100%' }">
                <a-form-item field="depositType" :rules="[{ required: true, message: '请选择保证金类型', trigger: 'change' }]" hide-label :style="{ marginBottom: '0', width: '100%' }">
                  <a-select v-model="formData.depositType" placeholder="请选择">
                    <a-option v-for="item in dictData[DictType.DEPOSIT_TYPE]" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </div>
              <div v-if="formData.depositType === 0" :style="{ width: '60%' }">
                <a-form-item field="depositAmount" :rules="[{ required: true, message: '请输入保证金金额', trigger: 'blur' }]" hide-label :style="{ marginBottom: '0', width: '100%' }">
                  <a-input-number v-model="formData.depositAmount" placeholder="请输入金额" :precision="2" :min="0"
                    style="width: 100%">
                    <template #append>元</template>
                  </a-input-number>
                </a-form-item>
              </div>
            </a-input-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="支付方式" field="paymentMethod">
            <a-select v-model="formData.paymentMethod" placeholder="请选择">
              <a-option value="1">月付</a-option>
              <a-option value="2">季付</a-option>
              <a-option value="3">半年付</a-option>
              <a-option value="4">年付</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="租赁期限" required>
            <a-input-group>
              <a-form-item field="minRentalPeriod" :rules="[{ required: true, message: '请输入最小租赁期限', trigger: 'blur' }]" hide-label :style="{ marginBottom: '0', width: '30%' }">
                <a-input-number v-model="formData.minRentalPeriod" placeholder="最小期限" :min="1" style="width: 100%" />
              </a-form-item>
              <div style="width: 30px; text-align: center; line-height: 32px;">至</div>
              <a-form-item field="maxRentalPeriod" :rules="[{ required: true, message: '请输入最大租赁期限', trigger: 'blur' }]" hide-label :style="{ marginBottom: '0', width: '30%' }">
                <a-input-number v-model="formData.maxRentalPeriod" placeholder="最大期限" :min="1" style="width: 100%" />
              </a-form-item>
              <a-form-item field="rentalPeriodUnit" :rules="[{ required: true, message: '请选择租赁期限单位', trigger: 'change' }]" hide-label :style="{ marginBottom: '0', width: 'calc(40% - 30px)' }">
                <a-select v-model="formData.rentalPeriodUnit" placeholder="请选择单位">
                  <a-option value="1">月</a-option>
                  <a-option value="2">年</a-option>
                </a-select>
              </a-form-item>
            </a-input-group>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 免租期 -->
      <SectionTitle title="免租期" style="margin-bottom: 16px;" />
      <a-row>
        <a-col :span="24">
          <a-form-item label="参考因素" field="freeRentType">
            <a-radio-group v-model="formData.freeRentType">
              <a-radio :value="1">不限</a-radio>
              <a-radio :value="2">租赁期限</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 不限时的免租期 -->
      <a-row v-if="formData.freeRentType === 1">
        <a-col :span="12">
          <a-form-item label="免租期" field="freeRentPeriod">
            <a-input-number v-model="formData.freeRentPeriod" placeholder="请输入" :min="0" style="width: 100%">
              <template #append>月</template>
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 租赁期限时的阶梯免租期 -->
      <div v-if="formData.freeRentType === 2">
        <a-row>
          <a-col :span="24">
            <a-form-item label="租赁期限N" required class="rental-period-form-item">
              <!-- 租赁期限阶梯内容区域 -->
              <div v-for="(item, index) in formData.freeRentSteps" :key="index" style="min-height: 52px;width: 100%;">
                <!-- 最后一条记录的特殊布局 -->
                <a-row v-if="index === formData.freeRentSteps.length - 1" style="align-items: flex-start;">
                  <a-col :span="10">
                    <a-input-number v-model="item.start" disabled style="width: 100%">
                      <template #append>年及以上</template>
                    </a-input-number>
                  </a-col>
                  <a-col :span="4" style="text-align: center;line-height: 32px;">
                    <span>免租期</span>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item
                      :field="`freeRentSteps.${index}.discount`"
                      :show-colon="false"
                      hide-label
                    >
                      <a-input-number v-model="item.discount" placeholder="请输入" :min="0" style="width: 100%">
                        <template #append>月</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <!-- 占位，保持对齐 -->
                  </a-col>
                </a-row>

                <!-- 其他记录的正常布局 -->
                <a-row v-else style="align-items: flex-start;">
                  <a-col :span="4">
                    <a-input-number v-model="item.start" disabled style="width: 100%" />
                  </a-col>
                  <a-col :span="2" style="text-align: center;line-height: 32px;">
                    <span>≤N<</span>
                  </a-col>
                  <a-col :span="4">
                    <a-form-item
                      :field="`freeRentSteps.${index}.end`"
                      :rules="[{ required: true, message: '请输入租赁期限', trigger: 'blur' }]"
                      :show-colon="false"
                      hide-label
                    >
                      <a-input-number v-model="item.end" placeholder="请输入" :min="getMinValue(index)"
                        style="width: 100%" @change="updateNextMinValue(index)">
                        <template #append>年</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="4" style="text-align: center;">
                    <span>免租期</span>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item
                      :field="`freeRentSteps.${index}.discount`"
                      :rules="[{ required: true, message: '请输入免租期', trigger: 'blur' }]"
                      :show-colon="false"
                      hide-label
                    >
                      <a-input-number v-model="item.discount" placeholder="请输入" :min="0" style="width: 100%">
                        <template #append>月</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <IconDelete v-if="index > 0" @click="removeFreeRentStep(index)"
                      style="color: #ff4d4f; cursor: pointer; font-size: 16px;margin-left: 16px;" />
                  </a-col>
                </a-row>
              </div>

              <a-row>
                <a-col :span="24">
                  <a-button type="text" @click="addFreeRentStep" style="padding: 0; color: #1890ff;">
                    添加阶梯
                  </a-button>
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconDelete } from '@arco-design/web-vue/es/icon'
import SectionTitle from '@/components/sectionTitle/index.vue'
import { DictType, dictData } from '@/dict/data'
import { useDictSync } from '@/utils/dict'

// 事件定义
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const propertyTypeDict = ref<Record<string | number, string>>({})

// 计算属性 - 用途显示名称
const displayRoomUsage = computed(() => {
  return propertyTypeDict.value[Number(formData.roomUsage)] || formData.roomUsage || ''
})

// 判断是否为商铺或综合体
const isShopOrComplex = computed(() => {
  return Number(formData.roomUsage) === 31 || Number(formData.roomUsage) === 32
})

// 判断是否为宿舍（需要显示户型字段）
const isDormitory = computed(() => {
  return Number(formData.roomUsage) === 10 // 10:宿舍
})

// 初始化用途字典数据
const initPropertyTypeDict = async () => {
    try {
        const dictResult = await useDictSync('diversification_purpose')
        // 将字典数据转换为映射格式，方便查询
        if (dictResult.diversification_purpose) {
            const dict: Record<string | number, string> = {}
            const flattenDict = (items: any[], dict: Record<string | number, string>) => {
                items.forEach(item => {
                    dict[item.value] = item.label
                    if (item.children && item.children.length > 0) {
                        flattenDict(item.children, dict)
                    }
                })
            }
            flattenDict(dictResult.diversification_purpose, dict)
            propertyTypeDict.value = dict
        }
    } catch (error) {
        console.error('获取多经用途字典数据失败:', error)
    }
}

// 在组件创建时获取字典数据
initPropertyTypeDict()

// 表单数据
const formData = reactive({
  // 房源基本信息（只读）
  roomName: '',
  plotName: '',
  buildingName: '',
  floorName: '',
  roomUsage: '',
  roomType: '',
  rentArea: null as number | null,

  // 定价信息（可编辑）
  planningBusiness: '',
  baseRent: null as number | null,
  additionalFee: null as number | null,
  calcUnit: null as number | null, // 计租单位(1元/平方米/月 2元/月 3元/日)
  isRentIncrease: 0, // 租金是否递增(0否 1是)
  increaseInterval: null as number | null, // 递增间隔(年)
  increaseRate: null as number | null, // 单价递增率(%)
  depositType: 0, // 保证金类型(固定金额等)
  depositAmount: null as number | null, // 保证金金额
  paymentMethod: '', // 支付方式(1月付 2季付 3半年付 4年付)
  minRentalPeriod: null as number | null, // 租赁期限(开始)
  maxRentalPeriod: null as number | null, // 租赁期限(结束)
  rentalPeriodUnit: '2', // 租赁期限单位(1月 2年)
  freeRentType: 1, // 免租期参考因素(1不限 2租赁期限)
  freeRentPeriod: null as number | null, // 免租期(月)
  freeRentSteps: [
    { start: 0, end: 1, discount: 0 },
    { start: 1, end: null, discount: 0 }
  ] as Array<{ start: number | null, end: number | null, discount: number | null }>,

  // 内部字段
  uniqueId: '',
  roomId: '',
  parcelId: '',
  buildingId: '',
  floorId: ''
})

// 表单验证规则
const formRules = {
  planningBusiness: [
    {
      required: true,
      message: '请选择规划业态',
      trigger: 'change',
      validator: (value: any, callback: any) => {
        if (isShopOrComplex.value && !value) {
          callback('请选择规划业态')
        } else {
          callback()
        }
      }
    }
  ],
  baseRent: [
    { required: true, message: '请输入基础租金', trigger: 'blur' }
  ],
  calcUnit: [
    { required: true, message: '请选择计租单位', trigger: 'change' }
  ],
  isRentIncrease: [
    { required: true, message: '请选择租金是否递增', trigger: 'change' }
  ],
  increaseInterval: [
    {
      required: true,
      message: '请输入递增间隔',
      trigger: 'blur',
      validator: (value: any, callback: any) => {
        if (formData.isRentIncrease === 1 && !value) {
          callback('请输入递增间隔')
        } else {
          callback()
        }
      }
    }
  ],
  increaseRate: [
    {
      required: true,
      message: '请输入单价递增率',
      trigger: 'blur',
      validator: (value: any, callback: any) => {
        if (formData.isRentIncrease === 1 && !value) {
          callback('请输入单价递增率')
        } else {
          callback()
        }
      }
    }
  ],
  // 注：保证金相关字段的验证已移至内联表单项
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  // 注：租赁期限相关字段的验证已移至内联表单项
  freeRentType: [
    { required: true, message: '请选择参考因素', trigger: 'change' }
  ]
}

// 显示抽屉
const show = (roomData: any) => {
  visible.value = true

  // 填充表单数据 - 直接使用接口字段名
  Object.assign(formData, {
    roomName: roomData.roomName || '',
    plotName: roomData.parcelName || '', // 使用接口真实字段 parcelName
    buildingName: roomData.buildingName || '',
    floorName: roomData.floorName || '',
    roomUsage: roomData.roomUsage || '', // 直接使用接口字段
    roomType: roomData.roomType || '',
    rentArea: roomData.rentArea || null,

    planningBusiness: roomData.planningBusiness || '',
    baseRent: roomData.baseRent || null,
    additionalFee: roomData.additionalFee || null,
    calcUnit: roomData.calcUnit || null,
    // 处理租金是否递增：接口可能返回布尔值或数字，统一转换为数字
    isRentIncrease: roomData.isRentIncrease === true || roomData.isRentIncrease === 1 ? 1 : 0,
    increaseInterval: roomData.increaseInterval || null,
    increaseRate: roomData.increaseRate || null,
    depositType: roomData.depositType ?? 0,
    depositAmount: roomData.depositAmount || null,
    paymentMethod: roomData.paymentMethod || '',
    // 处理租赁期限：接口可能返回字符串或数字，统一转换为数字
    minRentalPeriod: roomData.minRentalPeriod ? Number(roomData.minRentalPeriod) : null,
    maxRentalPeriod: roomData.maxRentalPeriod ? Number(roomData.maxRentalPeriod) : null,
    rentalPeriodUnit: roomData.rentalPeriodUnit || '2',
    freeRentType: roomData.freeRentType || 1,
    freeRentPeriod: roomData.freeRentPeriod || null,

    uniqueId: roomData.uniqueId || '',
    roomId: roomData.roomId || '',
    parcelId: roomData.parcelId || '', // 直接使用接口字段
    buildingId: roomData.buildingId || '',
    floorId: roomData.floorId || ''
  })

  // 初始化免租期阶梯数据
  if (roomData.freeRentSteps && Array.isArray(roomData.freeRentSteps)) {
    formData.freeRentSteps = roomData.freeRentSteps
  } else if (roomData.freeRentTerm && roomData.freeRentType === 2) {
    // 尝试从freeRentTerm解析JSON字符串
    try {
      const parsedSteps = JSON.parse(roomData.freeRentTerm)
      if (Array.isArray(parsedSteps)) {
        formData.freeRentSteps = parsedSteps
      } else {
        // 默认显示第一条和最后一条
        formData.freeRentSteps = [
          { start: 0, end: 1, discount: 0 },
          { start: 1, end: null, discount: 0 }
        ]
      }
    } catch (e) {
      console.error('解析freeRentTerm失败:', e)
      // 默认显示第一条和最后一条
      formData.freeRentSteps = [
        { start: 0, end: 1, discount: 0 },
        { start: 1, end: null, discount: 0 }
      ]
    }
  } else {
      // 默认显示第一条和最后一条
  formData.freeRentSteps = [
    { start: 0, end: 1, discount: 0 },
    { start: 1, end: null, discount: 0 }
  ]
  }

  // 确保数据链式关系正确
  for (let i = 1; i < formData.freeRentSteps.length; i++) {
    const previousStep = formData.freeRentSteps[i - 1]
    if (previousStep.end !== null && previousStep.end !== undefined) {
      formData.freeRentSteps[i].start = previousStep.end
    }
  }
}

// 确认保存
const handleConfirm = async () => {
  try {
    const errors = await formRef.value?.validate()
    if (errors) return

    loading.value = true

    // 构建更新数据
    const updateData = {
      uniqueId: formData.uniqueId,
      roomId: formData.roomId,
      roomName: formData.roomName,
      parcelId: formData.parcelId,
      parcelName: formData.plotName,
      buildingId: formData.buildingId,
      buildingName: formData.buildingName,
      floorId: formData.floorId,
      floorName: formData.floorName,
      roomUsage: formData.roomUsage,
      roomType: formData.roomType,
      rentArea: formData.rentArea,
      planningBusiness: formData.planningBusiness,
      baseRent: formData.baseRent,
      additionalFee: formData.additionalFee,
      calcUnit: formData.calcUnit,
      isRentIncrease: formData.isRentIncrease,
      increaseInterval: formData.increaseInterval,
      increaseRate: formData.increaseRate,
      depositType: formData.depositType,
      depositAmount: formData.depositAmount,
      paymentMethod: formData.paymentMethod,
      minRentalPeriod: formData.minRentalPeriod,
      maxRentalPeriod: formData.maxRentalPeriod,
      rentalPeriodUnit: formData.rentalPeriodUnit,
      freeRentType: formData.freeRentType,
      freeRentPeriod: formData.freeRentPeriod,
      freeRentSteps: formData.freeRentSteps,
      // 将阶梯免租期格式化为 JSON 字符串
      freeRentTerm: formData.freeRentType === 2 ? JSON.stringify(formData.freeRentSteps) : undefined
    }

    emit('success', updateData)
    visible.value = false
    Message.success('房源定价信息更新成功')

  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    loading.value = false
  }
}



// 获取当前步骤的最小值
const getMinValue = (index: number) => {
  if (index === 0) {
    return 0
  }
  const previousStep = formData.freeRentSteps[index - 1]
  return previousStep.end || 0
}

// 更新下一个步骤的最小值
const updateNextMinValue = async (index: number) => {
  // 等待当前输入完成
  await nextTick()

  const currentStep = formData.freeRentSteps[index]
  if (currentStep.end !== null && currentStep.end !== undefined) {
    // 检查是否有下一条记录需要更新
    if (index + 1 < formData.freeRentSteps.length) {
      // 更新下一个步骤的最小值
      formData.freeRentSteps[index + 1].start = currentStep.end

      // 触发表单验证更新
      await nextTick()
      formRef.value?.validateField(`freeRentSteps.${index + 1}.start`)
    }
  }
}

// 添加免租期阶梯
const addFreeRentStep = () => {
  const lastIndex = formData.freeRentSteps.length - 1
  const previousStep = formData.freeRentSteps[lastIndex - 1]

  // 在最后一个之前插入新的阶梯
  const newStep = {
    start: previousStep?.end || null, // 前一条的end值作为新阶梯的start值
    end: null,
    discount: 0
  }

  formData.freeRentSteps.splice(lastIndex, 0, newStep)
}

// 删除免租期阶梯
const removeFreeRentStep = (index: number) => {
  if (index > 0 && index < formData.freeRentSteps.length - 1) {
    formData.freeRentSteps.splice(index, 1)
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

// 监听免租期阶梯数据变化，自动更新起始值
watch(() => formData.freeRentSteps.map(step => step.end), async (newEndValues, oldEndValues) => {
  // 检查哪个步骤的结束值发生了变化
  for (let i = 0; i < newEndValues.length - 1; i++) {
    if (newEndValues[i] !== oldEndValues?.[i] && newEndValues[i] !== null && newEndValues[i] !== undefined) {
      // 更新下一个步骤的起始值，包括最后一条记录
      if (i + 1 < formData.freeRentSteps.length) {
        formData.freeRentSteps[i + 1].start = newEndValues[i]

        // 等待 DOM 更新后触发验证
        await nextTick()
        formRef.value?.validateField(`freeRentSteps.${i + 1}.start`)
      }
    }
  }
}, { deep: true })

// 暴露方法
defineExpose({
  show
})
</script>

<style scoped lang="less">
:deep(.arco-form-item-label) {
  font-weight: 500;
}

:deep(.arco-divider-text) {
  font-weight: 600;
  color: #1d2129;
}

:deep(.arco-input-group) {
  display: flex;
  align-items: center;
}

// 修复租赁期限阶梯的布局问题
:deep(.rental-period-form-item) {
  .arco-form-item-content {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  .arco-form-item-content-wrapper {
    width: 100%;
  }
  .arco-form-item{
    margin-bottom: 0;
  }
}
</style>
