<template>
    <a-drawer
        v-model:visible="visible"
        :title="drawerTitle"
        class="common-drawer"
        :footer="true"
        @cancel="handleCancel"
    >
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">{{ readOnlyMode ? '关闭' : '取消' }}</a-button>
                <template v-if="!readOnlyMode">
                    <a-button type="primary" @click="handleSubmit">提交</a-button>
                    <a-button @click="handleSave">暂存</a-button>
                </template>
            </a-space>
        </template>
        <div class="pricing-form">
            <!-- 统一表单 -->
            <a-form ref="formRef" :model="formData" :rules="formRules" label-align="right" layout="horizontal"
                :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" auto-label-width>

                <!-- 项目信息 -->
                <div class="form-section">
                    <SectionTitle title="项目信息" style="margin-bottom: 16px;" />
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item field="projectName" label="项目" required>
                                <a-input v-model="formData.projectName" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item field="pricingType" label="定价类型" required>
                                <a-select v-model="formData.pricingType" placeholder="请选择定价类型"
                                    :disabled="readOnlyMode">
                                    <a-option value="1">首次定价</a-option>
                                    <a-option value="2">过程调价</a-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>

                <!-- 定价的房源 -->
                <div class="form-section">
                    <SectionTitle title="定价的房源" />
                    <div class="list-header">
                        <div class="list-actions">
                            <div class="search-filters">
                                <a-select v-model="selectedPlotId" placeholder="全部" style="width: 150px; margin-right: 16px;"
                                    @change="handlePlotChange">
                                    <a-option value="">全部</a-option>
                                    <a-option v-for="plot in plotOptions" :key="plot.id" :value="plot.id">
                                        {{ plot.name }}
                                    </a-option>
                                </a-select>
                                <a-input-search v-model="searchKeyword" placeholder="请输入楼栋/房源名称搜索" allow-clear
                                    style="width: 360px;" @search="handleSearch" @clear="handleClearSearch" />
                            </div>
                            <a-space v-if="!readOnlyMode">
                                <a-button type="primary" @click="handleAddAsset">
                                    <template #icon><icon-plus /></template>
                                    添加房源
                                </a-button>
                                <a-button @click="handleExportRooms">
                                    <template #icon><icon-download /></template>
                                    导出定价房源
                                </a-button>
                                <a-button @click="handleImportPricing">
                                    <template #icon><icon-upload /></template>
                                    导入定价信息
                                </a-button>
                                <a-button @click="handleBatchSetFreeRent">
                                    <template #icon><icon-calendar /></template>
                                    批量设置免租期
                                </a-button>
                                <a-button @click="handleBatchRemove">
                                    <template #icon><icon-delete /></template>
                                    批量删除
                                </a-button>
                            </a-space>
                        </div>
                    </div>

                    <a-table ref="tableRef" :columns="columns" :data="paginatedTableData" :bordered="{ cell: true }"
                        row-key="uniqueId" v-model:selected-keys="selectedKeys" :scroll="{ x: 1 }"
                        :row-selection="readOnlyMode ? undefined : { type: 'checkbox', showCheckedAll: true }"
                        :pagination="tablePagination" @page-change="handleTablePageChange" @page-size-change="handleTablePageSizeChange">
                        <template #pricingType="{ record }">
                            {{ getPricingTypeText(record.pricingType) }}
                        </template>
                        <template #operation="{ record }">
                            <a-space v-if="!readOnlyMode">
                                <a-button type="text" size="mini" @click="handleEdit(record)">编辑</a-button>
                                <a-button type="text" size="mini" status="danger" @click="handleRemove(record)">删除</a-button>
                            </a-space>
                        </template>
                    </a-table>
                </div>
                <!-- 折扣方案 -->
                <div class="form-section">
                    <SectionTitle title="折扣方案" style="margin-bottom: 16px;" />
                    <div class="discount-schemes">
                        <div v-for="(scheme, index) in formData.discountSchemes" :key="scheme.id" class="discount-item">
                            <a-row :gutter="16">
                                <a-col :span="8">
                                    <a-form-item :field="`discountSchemes.${index}.post`" label="适用岗位" required>
                                        <a-select v-model="scheme.post" placeholder="请选择适用岗位" :disabled="readOnlyMode">
                                            <a-option value="0">不限</a-option>
                                            <a-option value="1">招商专员</a-option>
                                            <a-option value="2">招商经理</a-option>
                                            <a-option value="3">片区经理</a-option>
                                            <a-option value="4">事业部负责人</a-option>
                                        </a-select>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="8">
                                    <a-form-item :field="`discountSchemes.${index}.discount`" label="折扣" required>
                                        <a-input-number
                                            v-model="scheme.discount"
                                            placeholder="请输入折扣"
                                            :disabled="readOnlyMode"
                                            :min="0"
                                            :max="100"
                                            :precision="2"
                                            style="width: 100%"
                                        >
                                            <template #append>%</template>
                                        </a-input-number>
                                    </a-form-item>
                                </a-col>
                                <a-col :span="2" v-if="!readOnlyMode">
                                    <a-button type="text" status="danger" class="remove-btn" @click="removeDiscountScheme(index)">
                                        <template #icon><icon-delete /></template>
                                    </a-button>
                                </a-col>
                            </a-row>
                        </div>

                        <div v-if="!readOnlyMode" class="add-discount-btn">
                            <a-button type="primary" size="small" @click="addDiscountScheme">
                                <template #icon><icon-plus /></template>
                                添加折扣
                            </a-button>
                        </div>
                    </div>
                </div>

                <!-- 补充说明 -->
                <div class="form-section">
                    <SectionTitle title="补充说明" style="margin-bottom: 16px;" />
                    <a-row :gutter="16">
                        <a-col :span="16">
                            <a-form-item field="remark" label="备注说明">
                                <a-textarea v-model="formData.remark" placeholder="请输入备注说明" :disabled="readOnlyMode"
                                    :max-length="500" allow-clear show-word-limit />
                            </a-form-item>
                        </a-col>
                        <a-col :span="16">
                            <a-form-item field="attachment" label="相关附件">
                                <upload-file
                                 v-model="formData.attachment" 
                                 :readonly="readOnlyMode"
                                 :limit="10"
                                 accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
            </a-form>
        </div>
    </a-drawer>

    <!-- 楼栋选择弹框 -->
    <SelectBuildingModal
        v-if="showSelectBuildingModal"
        ref="selectBuildingModalRef"
        @next="handleSelectBuildingNext"
        @cancel="handleSelectBuildingCancel"
    />

    <!-- 房源选择弹框 -->
    <SelectRoomsModal
        v-if="showSelectRoomsModal"
        ref="selectRoomsModalRef"
        @prev="handleSelectRoomsPrev"
        @next="handleSelectRoomsNext"
        @cancel="handleSelectRoomsCancel"
    />

    <!-- 房源定价编辑抽屉 -->
    <RoomPricingEditDrawer
        v-if="showRoomPricingEditDrawer"
        ref="roomPricingEditDrawerRef"
        @success="handleRoomPricingSuccess"
        @cancel="handleRoomPricingCancel"
    />

    <!-- 批量设置免租期弹框 -->
    <BatchSetFreeRentModal
        v-if="showBatchSetFreeRentModal"
        ref="batchSetFreeRentModalRef"
        @success="handleBatchSetFreeRentSuccess"
        @cancel="handleBatchSetFreeRentCancel"
    />
</template>

<script setup lang="ts">
import { ref, computed, reactive, nextTick } from 'vue'
import type { PricingVo, PricingTemplateDTO } from '@/api/projectPricing'
import { downloadPricingTemplate, addPricing, updatePricing, getPricingDetail, readPricingTemplateData } from '@/api/projectPricing'
import { exportExcel } from '@/utils/exportUtil'
import { IconPlus, IconDelete, IconDownload, IconUpload, IconCalendar } from '@arco-design/web-vue/es/icon'
import SectionTitle from '@/components/sectionTitle/index.vue'
import uploadFile from '@/components/upload/uploadFile.vue'
import SelectBuildingModal from './SelectBuildingModal.vue'
import SelectRoomsModal from './SelectRoomsModal.vue'
import RoomPricingEditDrawer from './RoomPricingEditDrawer.vue'
import BatchSetFreeRentModal from './BatchSetFreeRentModal.vue'
import { Message, Modal } from '@arco-design/web-vue'
import { DictType, dictData } from '@/dict/data'
import { useDictSync } from '@/utils/dict'

// 事件定义
const emit = defineEmits<{
    success: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const mode = ref<'add' | 'edit' | 'view'>('add')
const currentRecord = ref<PricingVo | null>(null)
const readOnlyMode = ref(false)
const searchKeyword = ref('')
const selectedPlotId = ref('')
const plotOptions = ref<Array<{id: string, name: string}>>([])
const tableData = ref<any[]>([])
const originalTableData = ref<any[]>([])
const selectedKeys = ref<string[]>([])

// 分页相关数据
const tablePagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    pageSizeOptions: ['10', '20', '50', '100']
})



// 表单数据
const formRef = ref()
const formData = reactive({
    projectName: '',
    pricingType: '',
    applicationName: '',
    remark: '',
    attachment: '',
    pricingDesc: '',
    discountSchemes: [{
        id: `discount-${Date.now()}`,
        post: '0',
        discount: null
    }] as Array<{
        id: string
        post: string
        discount: number | null
    }>
})

// 表单校验规则
const formRules = computed(() => {
    const rules: any = {
        projectName: [
            { required: true, message: '项目不能为空', trigger: ['change', 'blur'] }
        ],
        pricingType: [
            { required: true, message: '请选择定价类型', trigger: ['change', 'blur'] }
        ]
    }

    // 动态添加折扣方案的校验规则
    formData.discountSchemes.forEach((_, index) => {
        rules[`discountSchemes.${index}.position`] = [
            { required: true, message: '请选择适用岗位', trigger: ['change', 'blur'] }
        ]
        rules[`discountSchemes.${index}.discount`] = [
            { required: true, message: '请输入折扣', trigger: ['change', 'blur'] },
            { type: 'number', min: 0, max: 100, message: '折扣必须在0-100之间', trigger: ['change', 'blur'] }
        ]
    })

    return rules
})

// 用途字典数据
const propertyTypeDict = ref<Record<string | number, string>>({})

// 初始化用途字典数据
const initPropertyTypeDict = async () => {
    try {
        const dictResult = await useDictSync('diversification_purpose')
        // 将字典数据转换为映射格式，方便查询
        if (dictResult.diversification_purpose) {
            const dict: Record<string | number, string> = {}
            const flattenDict = (items: any[], dict: Record<string | number, string>) => {
                items.forEach(item => {
                    dict[item.value] = item.label
                    if (item.children && item.children.length > 0) {
                        flattenDict(item.children, dict)
                    }
                })
            }
            flattenDict(dictResult.diversification_purpose, dict)
            propertyTypeDict.value = dict
        }
    } catch (error) {
        console.error('获取多经用途字典数据失败:', error)
    }
}

// 在组件创建时获取字典数据
initPropertyTypeDict()

// 计租单位转换
const getRentUnitText = (calcUnit: number | string): string => {
    if (calcUnit === undefined || calcUnit === null) return '-'
    const unitMap: Record<string, string> = {
        '1': '元/平方米/月',
        '2': '元/月',
        '3': '元/日'
    }
    return unitMap[String(calcUnit)] || '-'
}

// 支付方式转换
const getPaymentMethodText = (method: string): string => {
    if (!method) return '-'
    const methodMap: Record<string, string> = {
        '1': '月付',
        '2': '季付',
        '3': '半年付',
        '4': '年付'
    }
    return methodMap[method] || '-'
}

// 表格列定义
const columns = computed(() => {
    const baseColumns = [
    { title: '序号', dataIndex: 'index', width: 80, align: 'center' },
    { title: '房源名称', dataIndex: 'roomName', width: 120, align: 'center', ellipsis: true, tooltip: true },
    { title: '所属地块', dataIndex: 'parcelName', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { title: '楼栋', dataIndex: 'buildingName', width: 100, ellipsis: true, tooltip: true, align: 'center' },
    { title: '楼层', dataIndex: 'floorName', width: 80, align: 'center', ellipsis: true, tooltip: true },
    {
        title: '用途',
        dataIndex: 'roomUsage',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: { record: any }) => {
            // 获取用途对应的中文名称
            return propertyTypeDict.value[record.roomUsage] || record.roomUsage || '-'
        }
    },
    { title: '户型', dataIndex: 'roomType', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { 
        title: '计租面积㎡', 
        dataIndex: 'rentArea', 
        width: 120, 
        align: 'center', 
        ellipsis: true, 
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return record.rentArea ? Number(record.rentArea).toFixed(2) : '-'
        }
    },
    { title: '规划业态', dataIndex: 'planningBusiness', width: 100, align: 'center', ellipsis: true, tooltip: true },
    { 
        title: '基础租金', 
        dataIndex: 'baseRent', 
        width: 100, 
        align: 'center', 
        ellipsis: true, 
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return record.baseRent ? Number(record.baseRent).toFixed(2) : '-'
        }
    },
    { 
        title: '附加费用', 
        dataIndex: 'additionalFee', 
        width: 100, 
        align: 'center', 
        ellipsis: true, 
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return record.additionalFee ? Number(record.additionalFee).toFixed(2) : '-'
        }
    },
    {
        title: '计租单位',
        dataIndex: 'calcUnit',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return getRentUnitText(record.calcUnit)
        }
    },
    { 
        title: '递增间隔（年）', 
        dataIndex: 'increaseInterval', 
        width: 140, 
        align: 'center', 
        ellipsis: true, 
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return record.isRentIncrease ? (record.increaseInterval || '-') : '不递增'
        }
    },
    { 
        title: '单价递增率', 
        dataIndex: 'increaseRate', 
        width: 120, 
        align: 'center', 
        ellipsis: true, 
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return record.isRentIncrease ? (record.increaseRate ? `${record.increaseRate}%` : '-') : '-'
        }
    },
    { 
        title: '保证金', 
        dataIndex: 'deposit', 
        width: 100, 
        align: 'center', 
        ellipsis: true, 
        tooltip: true,
        render: ({ record }: { record: any }) => {
            // 从字典获取保证金类型名称
            if (record.depositType === 0) {
                return record.depositAmount ? `${record.depositAmount}元` : '-'
            } else {
                const depositTypeLabel = dictData[DictType.DEPOSIT_TYPE]?.find(item => item.value === record.depositType)?.label
                return depositTypeLabel || '-'
            }
        }
    },
    {
        title: '租赁期限',
        dataIndex: 'minRentalPeriod',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: { record: any }) => {
            if (record.minRentalPeriod && record.maxRentalPeriod) {
                const unit = record.rentalPeriodUnit === '1' ? '月' : '年'
                return `${record.minRentalPeriod}-${record.maxRentalPeriod}${unit}`
            }
            return '-'
        }
    },
    { 
        title: '支付方式', 
        dataIndex: 'paymentMethod', 
        width: 100, 
        align: 'center', 
        ellipsis: true, 
        tooltip: true,
        render: ({ record }: { record: any }) => {
            return getPaymentMethodText(record.paymentMethod)
        }
    },
    {
        title: '免租期',
        dataIndex: 'freeRentPeriod',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
        render: ({ record }: { record: any }) => {
            if (record.freeRentType === 1) {
                return record.freeRentPeriod ? `${record.freeRentPeriod}个月` : '-'
            } else if (record.freeRentType === 2) {
                // 尝试从freeRentTerm解析阶梯免租期数据
                try {
                    if (record.freeRentTerm) {
                        const steps = JSON.parse(record.freeRentTerm)
                        if (Array.isArray(steps) && steps.length > 0) {
                            return steps.map((step: any, index: number) => {
                                // 根据是否是最后一个阶梯确定展示格式
                                if (index === steps.length - 1 && step.start !== null) {
                                    // 最后一个阶梯，一般格式为"N年及以上"
                                    return `租赁期限≥${step.start}年免租期${step.discount}个月`
                                } else if (step.start !== null && step.end !== null) {
                                    // 中间阶梯，格式为"start≤N<end"
                                    return `租赁期限${step.start}≤N<${step.end}年免租期${step.discount}个月`
                                } else if (step.end !== null) {
                                    // 第一个阶梯，格式为"N<end"
                                    return `租赁期限<${step.end}年免租期${step.discount}个月`
                                }
                                return ''
                            }).filter(Boolean).join('，')
                        }
                    }
                    
                    // 如果有阶梯数据，但无法解析为有效格式
                    if (record.freeRentSteps && Array.isArray(record.freeRentSteps)) {
                        return record.freeRentSteps.map((step: any, index: number) => {
                            // 根据是否是最后一个阶梯确定展示格式
                            if (index === record.freeRentSteps.length - 1 && step.start !== null) {
                                // 最后一个阶梯，一般格式为"N年及以上"
                                return `租赁期限≥${step.start}年免租期${step.discount}个月`
                            } else if (step.start !== null && step.end !== null) {
                                // 中间阶梯，格式为"start≤N<end"
                                return `租赁期限${step.start}≤N<${step.end}年免租期${step.discount}个月`
                            } else if (step.end !== null) {
                                // 第一个阶梯，格式为"N<end"
                                return `租赁期限<${step.end}年免租期${step.discount}个月`
                            }
                            return ''
                        }).filter(Boolean).join('，')
                    }
                } catch (e) {
                    console.error('解析免租期阶梯数据失败:', e)
                }
                
                // 默认显示
                return '阶梯免租'
            } else {
                return '-'
            }
        }
    }
    ]

    // 如果不是只读模式，添加操作列
    if (!readOnlyMode.value) {
        baseColumns.push({ title: '操作', slotName: 'operation', width: 120, fixed: 'right' } as any)
    }

    return baseColumns
})

// 计算属性
const drawerTitle = computed(() => {
    const titleMap = {
        add: '新增立项定价申请',
        edit: '编辑立项定价申请',
        view: '查看立项定价申请'
    }
    return titleMap[mode.value]
})



// 分页后的表格数据
const paginatedTableData = computed(() => {
    const start = (tablePagination.current - 1) * tablePagination.pageSize
    const end = start + tablePagination.pageSize
    return tableData.value.slice(start, end)
})

// 表格分页变化
const handleTablePageChange = (current: number) => {
    tablePagination.current = current
}

// 表格每页条数变化
const handleTablePageSizeChange = (pageSize: number) => {
    tablePagination.pageSize = pageSize
    tablePagination.current = 1
}

// 定价类型转换函数
const getPricingTypeText = (type: string | undefined): string => {
    if (type === undefined || type === null || type === '') {
        return ''
    }
    
    const typeMap: Record<string, string> = {
        '1': '首次定价',
        '2': '过程调价'
    }
    return typeMap[String(type)] || type
}

// 方法
const show = (type: 'add' | 'edit' | 'view', record?: PricingVo | any) => {
    mode.value = type
    currentRecord.value = record || null
    readOnlyMode.value = type === 'view'
    visible.value = true
    console.log(record)
    // 如果是新增模式且传入了房源数据
    if (type === 'add' && record && record.selectedRooms) {
        // 设置项目信息
        formData.projectName = record.projectName || ''

        // 保存项目数据供添加房源时使用
        currentProjectData.value = {
            projectId: record.projectId,
            projectName: record.projectName
        }

        // 处理选中的房源数据
        if (record.selectedRooms && record.selectedRooms.length > 0) {
            tableData.value = record.selectedRooms.map((room: any, index: number) => ({
                uniqueId: `room-${Date.now()}-${index}`,
                index: index + 1,
                roomId: room.id,
                roomName: room.name || room.roomName || '',
                parcelId: room.plotId || room.parcelId || '',
                plotId: room.plotId || room.parcelId || '', // 兼容两种字段名
                parcelName: room.plotName || room.parcelName || '',
                plotName: room.plotName || room.parcelName || '', // 兼容两种字段名
                buildingId: room.buildingId,
                buildingName: room.buildingName || '',
                building: room.buildingName || '', // 兼容字段名
                floorId: room.floorId,
                floorName: room.floorName || '',
                floor: room.floorName || '', // 兼容字段名
                roomUsage: room.roomUsage || room.propertyType || '', // 优先使用字典值
                propertyType: room.propertyType || room.roomUsage || '', // 兼容字段名
                roomType: room.layoutName || room.roomType || '',
                layout: room.layoutName || room.roomType || '', // 兼容字段名
                rentArea: room.rentArea || 0,
                pricingType: formData.pricingType || '1', // 默认首次定价
                rentPrice: room.rentPrice || '',
                propertyPrice: room.propertyPrice || ''
            }))
            originalTableData.value = [...tableData.value]

            // 生成地块选项
            const plots = new Map()
            record.selectedRooms.forEach((room: any) => {
                if (room.plotId && room.plotName) {
                    plots.set(room.plotId, room.plotName)
                }
            })
            plotOptions.value = Array.from(plots.entries()).map(([id, name]) => ({ id, name }))

            // 重置地块选择
            selectedPlotId.value = ''

            // 更新分页总数
            tablePagination.total = tableData.value.length
            tablePagination.current = 1
        }
    }
    // 如果是编辑或查看模式，需要加载数据
    else if (record && (type === 'edit' || type === 'view')) {
        loading.value = true
        // 先从接口获取完整数据
        getPricingDetail(record.id)
            .then(response => {
                const detailData = response.data
                
                // 保存项目数据供添加房源时使用
                currentProjectData.value = {
                    projectId: detailData.projectId,
                    projectName: detailData.projectName
                }
                
                // 加载表单数据
                formData.projectName = detailData.projectName || ''
                formData.pricingType = detailData.pricingType?.toString() || ''
                formData.remark = detailData.pricingDetails || ''
                formData.attachment = detailData.attachments || ''

                // 尝试解析折扣方案数据
                try {
                    if (detailData.discountRules) {
                        const discountSchemes = JSON.parse(detailData.discountRules)
                        if (Array.isArray(discountSchemes) && discountSchemes.length > 0) {
                                                         formData.discountSchemes = discountSchemes.map((scheme: any) => ({
                                id: scheme.id || `discount-${Date.now()}-${Math.random()}`,
                                post: scheme.post || scheme.position || '0',
                                discount: scheme.discount || null
                            }))
                        }
                    }
                } catch (e) {
                    console.error('解析折扣方案数据失败:', e)
                    formData.discountSchemes = [{
                        id: `discount-${Date.now()}`,
                        post: '0',
                        discount: null
                    }]
                }

                // 加载表格数据 - 直接使用接口字段
                if (detailData.roomList && detailData.roomList.length > 0) {
                    tableData.value = detailData.roomList.map((room: any, index: number) => ({
                        ...room,
                        index: index + 1,
                        uniqueId: `room-${index}`,
                        // 处理租金是否递增：接口可能返回布尔值或数字，统一转换为数字
                        isRentIncrease: room.isRentIncrease === true || room.isRentIncrease === 1 ? 1 : 0,
                        // 处理租赁期限：接口可能返回字符串或数字，统一转换为数字
                        minRentalPeriod: room.minRentalPeriod ? Number(room.minRentalPeriod) : null,
                        maxRentalPeriod: room.maxRentalPeriod ? Number(room.maxRentalPeriod) : null
                    }))
                    originalTableData.value = [...tableData.value]
                    
                    // 生成地块选项
                    const plots = new Map()
                    detailData.roomList.forEach((room: any) => {
                        if (room.parcelId && room.parcelName) {
                            plots.set(room.parcelId, room.parcelName)
                        }
                    })
                    plotOptions.value = Array.from(plots.entries()).map(([id, name]) => ({ id, name }))
                    
                    // 更新分页总数
                    tablePagination.total = tableData.value.length
                    tablePagination.current = 1
                }
            })
            .catch(error => {
                console.error('获取立项定价详情失败:', error)
            })
            .finally(() => {
                loading.value = false
            })
    }
}

const handleCancel = () => {
    visible.value = false
    currentRecord.value = null
    // 重置表单
    formData.projectName = ''
    formData.pricingType = ''
    formData.applicationName = ''
    formData.remark = ''
    formData.attachment = ''
    formData.pricingDesc = ''
    formData.discountSchemes = [{
        id: `discount-${Date.now()}`,
        post: '0',
        discount: null
    }]
    // 清空表格数据
    tableData.value = []
    originalTableData.value = []
    // 清空选中状态
    selectedKeys.value = []
    // 重置分页状态
    tablePagination.current = 1
    tablePagination.total = 0
}

const handleSubmit = async () => {
    const errors = await formRef.value.validate()
    if (errors) return

    // 检查房源列表是否为空
    if (!tableData.value || tableData.value.length === 0) {
        Message.warning('请至少添加一个房源')
        return
    }

    try {
        loading.value = true

        // 构建提交数据
        const submitData: PricingVo = {
            id: currentRecord.value?.id,
            applicationName: formData.applicationName || `${formData.projectName}立项定价申请`, // 默认申请名称
            pricingType: Number(formData.pricingType),
            projectId: currentProjectData.value?.projectId,
            projectName: formData.projectName,
            status: 1, // 提交状态为1（审批中）
            roomCount: tableData.value.length,
            pricingDesc: formData.pricingDesc || null,
            pricingDetails: formData.remark || null,
            attachments: formData.attachment || null,
            // 处理房源列表，直接使用接口字段
            roomList: tableData.value.map(room => ({
                id: room.id,
                pricingId: currentRecord.value?.id,
                roomId: room.roomId,
                roomName: room.roomName,
                parcelId: room.parcelId,
                parcelName: room.parcelName,
                buildingId: room.buildingId,
                buildingName: room.buildingName,
                floorId: room.floorId,
                floorName: room.floorName,
                roomUsage: room.roomUsage,
                roomType: room.roomType,
                rentArea: room.rentArea,
                planningBusiness: room.planningBusiness,
                baseRent: room.baseRent,
                additionalFee: room.additionalFee,
                calcUnit: room.calcUnit,
                // 确保类型正确
                isRentIncrease: room.isRentIncrease === 1 || room.isRentIncrease === true,
                increaseInterval: room.increaseInterval,
                increaseRate: room.increaseRate,
                depositType: room.depositType,
                depositAmount: room.depositAmount,
                paymentMethod: room.paymentMethod,
                minRentalPeriod: String(room.minRentalPeriod || ''),
                maxRentalPeriod: String(room.maxRentalPeriod || ''),
                rentalPeriodUnit: room.rentalPeriodUnit,
                freeRentType: room.freeRentType,
                freeRentPeriod: room.freeRentPeriod,
                freeRentTerm: room.freeRentType === 2 && room.freeRentSteps ? JSON.stringify(room.freeRentSteps) : undefined
            }))
        };

        // 设置折扣规则
        if (formData.discountSchemes && formData.discountSchemes.length > 0) {
            // 将折扣方案转换为后端需要的格式：{"post": xxx, "discount": xxx}
            const discountRules = formData.discountSchemes.map(scheme => ({
                post: scheme.post,
                discount: scheme.discount
            }));
            submitData.discountRules = JSON.stringify(discountRules);
        }

        // 调用API保存数据
        if (mode.value === 'add') {
            await addPricing(submitData);
            Message.success('立项定价申请提交成功');
        } else if (mode.value === 'edit') {
            await updatePricing(submitData);
            Message.success('立项定价申请更新成功');
        }

        // 提交成功
        emit('success')
        handleCancel()
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        loading.value = false
    }
}

const handleSave = async () => {
    // 进行表单必填校验
    const errors = await formRef.value.validate()
    if (errors) return

    // 检查项目信息
    if (!formData.projectName || !currentProjectData.value?.projectId) {
        Message.warning('请选择项目')
        return
    }

    try {
        loading.value = true

        // 构建暂存数据
        const saveData: PricingVo = {
            id: currentRecord.value?.id,
            applicationName: formData.applicationName || `${formData.projectName}立项定价申请`, // 默认申请名称
            pricingType: Number(formData.pricingType),
            projectId: currentProjectData.value?.projectId,
            projectName: formData.projectName,
            status: 0, // 暂存状态为0（草稿）
            roomCount: tableData.value.length,
            pricingDesc: formData.pricingDesc || null,
            pricingDetails: formData.remark || null,
            attachments: formData.attachment || null,
            // 处理房源列表，直接使用接口字段
            roomList: tableData.value.map(room => ({
                id: room.id,
                pricingId: currentRecord.value?.id,
                roomId: room.roomId,
                roomName: room.roomName,
                parcelId: room.parcelId,
                parcelName: room.parcelName,
                buildingId: room.buildingId,
                buildingName: room.buildingName,
                floorId: room.floorId,
                floorName: room.floorName,
                roomUsage: room.roomUsage,
                roomType: room.roomType,
                rentArea: room.rentArea,
                planningBusiness: room.planningBusiness,
                baseRent: room.baseRent,
                additionalFee: room.additionalFee,
                calcUnit: room.calcUnit,
                // 确保类型正确
                isRentIncrease: room.isRentIncrease === 1 || room.isRentIncrease === true,
                increaseInterval: room.increaseInterval,
                increaseRate: room.increaseRate,
                depositType: room.depositType,
                depositAmount: room.depositAmount,
                paymentMethod: room.paymentMethod,
                minRentalPeriod: String(room.minRentalPeriod || ''),
                maxRentalPeriod: String(room.maxRentalPeriod || ''),
                rentalPeriodUnit: room.rentalPeriodUnit,
                freeRentType: room.freeRentType,
                freeRentPeriod: room.freeRentPeriod,
                freeRentTerm: room.freeRentType === 2 && room.freeRentSteps ? JSON.stringify(room.freeRentSteps) : undefined
            }))
        };

        // 设置折扣规则
        if (formData.discountSchemes && formData.discountSchemes.length > 0) {
            // 将折扣方案转换为后端需要的格式：{"post": xxx, "discount": xxx}
            const discountRules = formData.discountSchemes.map(scheme => ({
                post: scheme.post,
                discount: scheme.discount
            }));
            saveData.discountRules = JSON.stringify(discountRules);
        }

        // 调用API保存数据
        if (mode.value === 'add') {
            await addPricing(saveData);
            Message.success('立项定价申请已暂存');
        } else if (mode.value === 'edit') {
            await updatePricing(saveData);
            Message.success('立项定价申请已暂存');
        }

        // 暂存成功
        emit('success')
        handleCancel()
    } catch (error) {
        console.error('暂存失败:', error)
    } finally {
        loading.value = false
    }
}

// 搜索功能
const handleSearch = (value: string) => {
    searchKeyword.value = value
    filterTableDataByPlot()
}

// 清空搜索
const handleClearSearch = () => {
    searchKeyword.value = ''
    filterTableDataByPlot()
}

// 添加资产
const handleAddAsset = () => {
    // 检查是否有项目信息
    if (!currentProjectData.value?.projectId) {
        Message.warning('请先选择项目')
        return
    }

    // 显示楼栋选择弹框
    showSelectBuildingModal.value = true
    nextTick(() => {
        selectBuildingModalRef.value?.show({
            projectId: currentProjectData.value.projectId
        })
    })
}

// 导出定价房源
const handleExportRooms = async () => {
    if (!tableData.value || tableData.value.length === 0) {
        Message.warning('暂无房源数据可导出')
        return
    }

    if (!currentProjectData.value?.projectId) {
        Message.warning('请先选择项目')
        return
    }

    try {
        // 准备导出参数
        const exportParams: PricingTemplateDTO = {
            pageNum: 1,
            pageSize: 9999,
            projectId: currentProjectData.value.projectId,
            roomIdList: tableData.value.map(item => item.roomId)
        }

        // 使用 exportExcel 工具函数导出
        await exportExcel(downloadPricingTemplate, exportParams, '房源定价模板')
        Message.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
    }
}

// 导入定价信息
const handleImportPricing = () => {
    if (!currentProjectData.value?.projectId) {
        Message.warning('请先选择项目')
        return
    }

    // 创建文件输入元素
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.accept = '.xlsx,.xls'
    fileInput.style.display = 'none'
    document.body.appendChild(fileInput)

    // 监听文件选择事件
    fileInput.addEventListener('change', async (event) => {
        const files = (event.target as HTMLInputElement).files
        if (files && files.length > 0) {
            const file = files[0]

            // 验证文件类型
            const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']
            if (!allowedTypes.includes(file.type)) {
                Message.error('请选择Excel文件(.xlsx或.xls格式)')
                document.body.removeChild(fileInput)
                return
            }

            // 显示加载状态
            const loadingInstance = Message.loading({
                content: '正在导入定价数据...',
                duration: 0
            })

            try {
                // 创建FormData对象
                const formData = new FormData()
                formData.append('file', file)

                // 使用 readPricingTemplateData 接口读取文件数据
                const response = await readPricingTemplateData(formData)

                if (response && response.code === 200 && response.data) {
                    // 处理导入的定价数据，更新表格显示
                    const importedData = response.data

                    if (Array.isArray(importedData) && importedData.length > 0) {
                        // 处理导入的房源数据，添加到当前表格中
                        const newRooms = importedData.map((room: any, index: number) => ({
                            uniqueId: `imported-room-${Date.now()}-${index}`,
                            index: tableData.value.length + index + 1,
                            roomId: room.roomId || '',
                            roomName: room.roomName || '',
                            parcelId: room.parcelId || '',
                            parcelName: room.parcelName || '',
                            buildingId: room.buildingId || '',
                            buildingName: room.buildingName || '',
                            floorId: room.floorId || '',
                            floorName: room.floorName || '',
                            roomUsage: room.propertyType || room.propertyTypeName || '',
                            roomType: room.roomTypeName || '',
                            rentArea: room.rentArea || 0,
                            planningBusiness: room.planningBusiness || '',
                            baseRent: room.baseRent || 0,
                            additionalFee: room.additionalFee || 0,
                            calcUnit: room.calcUnit || 1,
                            isRentIncrease: room.isRentIncrease || 0,
                            increaseInterval: room.increaseInterval || 0,
                            increaseRate: room.increaseRate || 0,
                            depositType: room.depositType || 0,
                            depositAmount: room.depositAmount || 0,
                            paymentMethod: room.paymentMethod || '',
                            minRentalPeriod: room.minRentalPeriod || null,
                            maxRentalPeriod: room.maxRentalPeriod || null,
                            rentalPeriodUnit: room.rentalPeriodUnit || '',
                            freeRentType: room.freeRentType || 1,
                            freeRentPeriod: room.freeRentPeriod || 0,
                            freeRentTerm: room.freeRentTerm || undefined
                        }))

                        // 添加到表格数据中
                        tableData.value = [...tableData.value, ...newRooms]
                        originalTableData.value = [...tableData.value]

                        // 重新设置序号
                        tableData.value.forEach((item, index) => {
                            item.index = index + 1
                        })

                        // 更新地块选项
                        const plots = new Map()
                        tableData.value.forEach(item => {
                            if (item.parcelId && item.parcelName) {
                                plots.set(item.parcelId, item.parcelName)
                            }
                        })
                        plotOptions.value = Array.from(plots.entries()).map(([id, name]) => ({ id, name }))

                        // 更新分页信息
                        tablePagination.total = tableData.value.length

                        Message.success(`成功导入 ${newRooms.length} 条定价信息`)
                    } else {
                        Message.warning('文件中没有找到有效的定价数据')
                    }
                } else {
                    Message.error('读取文件失败，请检查文件格式')
                }
            } catch (error) {
                console.error('导入失败:', error)
            } finally {
                // 关闭加载状态
                loadingInstance.close()
                // 移除input元素
                document.body.removeChild(fileInput)
            }
        } else {
            document.body.removeChild(fileInput)
        }
    })

    // 触发文件选择
    fileInput.click()
}

// 批量设置免租期
const handleBatchSetFreeRent = () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择需要设置免租期的房源')
        return
    }

    // 显示批量设置免租期弹框
    showBatchSetFreeRentModal.value = true
    nextTick(() => {
        batchSetFreeRentModalRef.value?.show(selectedKeys.value.length)
    })
}

// 批量移除
const handleBatchRemove = () => {
    if (selectedKeys.value.length === 0) {
        Message.warning('请先选择需要删除的房源')
        return
    }
    
    Modal.confirm({
        title: '批量删除房源',
        content: `确定要删除选中的${selectedKeys.value.length}个房源吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
            // 移除选中的行
            tableData.value = tableData.value.filter(item => !selectedKeys.value.includes(item.uniqueId))
            originalTableData.value = [...tableData.value]
            
            // 重新设置序号
            tableData.value.forEach((item, index) => {
                item.index = index + 1
            })
            
            // 强制清空选中状态
            selectedKeys.value = []
            
            // 更新分页信息
            tablePagination.total = tableData.value.length
            
            // 如果当前页没有数据了，回到上一页
            const maxPage = Math.ceil(tablePagination.total / tablePagination.pageSize)
            if (tablePagination.current > maxPage && maxPage > 0) {
                tablePagination.current = maxPage
            }
            
            Message.success('已删除选中房源')
        }
    })
}

// 移除单行
const handleRemove = (record: any) => {
    // 从数据源中移除
    tableData.value = tableData.value.filter(item => item.uniqueId !== record.uniqueId)
    originalTableData.value = [...tableData.value]
    
    // 从选中项中移除
    if (selectedKeys.value.includes(record.uniqueId)) {
        selectedKeys.value = selectedKeys.value.filter(key => key !== record.uniqueId)
    }
    
    // 重新设置序号
    tableData.value.forEach((item, index) => {
        item.index = index + 1
    })
    
    // 更新分页信息
    tablePagination.total = tableData.value.length
    
    // 如果当前页没有数据了，回到上一页
    const maxPage = Math.ceil(tablePagination.total / tablePagination.pageSize)
    if (tablePagination.current > maxPage && maxPage > 0) {
        tablePagination.current = maxPage
    }
    
    Message.success('已删除房源')
}

// 房源选择弹框相关
const showSelectBuildingModal = ref(false)
const showSelectRoomsModal = ref(false)
const selectBuildingModalRef = ref()
const selectRoomsModalRef = ref()
const currentProjectData = ref<any>(null)

// 房源定价编辑相关
const showRoomPricingEditDrawer = ref(false)
const roomPricingEditDrawerRef = ref()
const currentEditingRoom = ref<any>(null)

// 批量设置免租期相关
const showBatchSetFreeRentModal = ref(false)
const batchSetFreeRentModalRef = ref()

// 楼栋选择相关处理函数
const handleSelectBuildingNext = (buildingData: any) => {
    showSelectBuildingModal.value = false
    showSelectRoomsModal.value = true
    nextTick(() => {
        selectRoomsModalRef.value?.show(buildingData, 'drawer')
    })
}

const handleSelectBuildingCancel = () => {
    showSelectBuildingModal.value = false
}

// 房源选择相关处理函数
const handleSelectRoomsPrev = (prevData: any) => {
    showSelectRoomsModal.value = false
    showSelectBuildingModal.value = true
    nextTick(() => {
        selectBuildingModalRef.value?.show(prevData)
    })
}

const handleSelectRoomsNext = (roomsData: any) => {
    // 处理选中的房源数据，去重并添加到表格
    if (roomsData.selectedRooms && roomsData.selectedRooms.length > 0) {
        const existingRoomIds = new Set(tableData.value.map(item => item.roomId))

        // 分离新房源和重复房源
        const newRooms: any[] = []
        const duplicateRooms: any[] = []

        roomsData.selectedRooms.forEach((room: any, index: number) => {
            if (existingRoomIds.has(room.id)) {
                duplicateRooms.push(room)
            } else {
                // 直接使用接口字段，不添加兼容性字段
                newRooms.push({
                    uniqueId: `room-${Date.now()}-${index}`,
                    index: tableData.value.length + newRooms.length + 1,
                    roomId: room.id,
                    roomName: room.name || room.roomName || '',
                    parcelId: room.parcelId || '',
                    parcelName: room.parcelName || '',
                    buildingId: room.buildingId,
                    buildingName: room.buildingName || '',
                    floorId: room.floorId,
                    floorName: room.floorName || '',
                    roomUsage: room.roomUsage || '',
                    roomType: room.roomType || '',
                    rentArea: room.rentArea || 0,
                    planningBusiness: room.planningBusiness || '',
                    baseRent: room.baseRent || 0,
                    additionalFee: room.additionalFee || 0,
                    calcUnit: room.calcUnit || 1,
                    isRentIncrease: room.isRentIncrease || 0,
                    increaseInterval: room.increaseInterval || 0,
                    increaseRate: room.increaseRate || 0,
                    depositType: room.depositType || 0,
                    depositAmount: room.depositAmount || 0,
                    minRentalPeriod: room.minRentalPeriod || null,
                    maxRentalPeriod: room.maxRentalPeriod || null,
                    rentalPeriodUnit: room.rentalPeriodUnit || undefined,
                    paymentMethod: room.paymentMethod || undefined,
                    freeRentType: room.freeRentType || 1,
                    freeRentPeriod: room.freeRentPeriod || 0,
                    freeRentTerm: room.freeRentType === 2 && room.freeRentSteps ? JSON.stringify(room.freeRentSteps) : undefined
                })
            }
        })

        // 处理添加结果
        if (newRooms.length > 0) {
            // 添加到表格数据中
            tableData.value = [...tableData.value, ...newRooms]
            originalTableData.value = [...tableData.value]

            // 重新设置序号
            tableData.value.forEach((item, index) => {
                item.index = index + 1
            })

            // 更新地块选项
            const plots = new Map()
            tableData.value.forEach(item => {
                if (item.parcelId && item.parcelName) {
                    plots.set(item.parcelId, item.parcelName)
                }
            })
            plotOptions.value = Array.from(plots.entries()).map(([id, name]) => ({ id, name }))

            // 更新分页信息
            tablePagination.total = tableData.value.length

            // 显示成功消息
            Message.success(`成功添加 ${newRooms.length} 个房源`)
        }
    }

    // 关闭弹框
    showSelectRoomsModal.value = false
}

const handleSelectRoomsCancel = () => {
    showSelectRoomsModal.value = false
}

// 房源定价编辑相关方法
const handleEdit = (record: any) => {
    currentEditingRoom.value = record
    showRoomPricingEditDrawer.value = true
    // 等待组件渲染后再调用 show 方法
    nextTick(() => {
        roomPricingEditDrawerRef.value?.show(record)
    })
}

const handleRoomPricingSuccess = (updatedData: any) => {
    // 房源定价编辑成功后，更新表格中对应的数据
    if (currentEditingRoom.value && updatedData) {
        const index = tableData.value.findIndex(item => item.uniqueId === updatedData.uniqueId)
        if (index !== -1) {
            // 更新定价信息，直接使用接口字段
            const updatedRoom = {
                ...tableData.value[index],
                // 基本信息保持不变，只更新可编辑部分
                planningBusiness: updatedData.planningBusiness,
                baseRent: updatedData.baseRent,
                additionalFee: updatedData.additionalFee,
                calcUnit: updatedData.calcUnit,
                isRentIncrease: updatedData.isRentIncrease,
                increaseInterval: updatedData.increaseInterval,
                increaseRate: updatedData.increaseRate,
                depositType: updatedData.depositType,
                depositAmount: updatedData.depositAmount,
                paymentMethod: updatedData.paymentMethod,
                minRentalPeriod: updatedData.minRentalPeriod,
                maxRentalPeriod: updatedData.maxRentalPeriod,
                rentalPeriodUnit: updatedData.rentalPeriodUnit,
                freeRentType: updatedData.freeRentType,
                freeRentPeriod: updatedData.freeRentPeriod,
                freeRentSteps: updatedData.freeRentSteps,
                freeRentTerm: updatedData.freeRentType === 2 && updatedData.freeRentSteps ? JSON.stringify(updatedData.freeRentSteps) : undefined
            }

            tableData.value[index] = updatedRoom
            originalTableData.value = [...tableData.value]
        }
    }

    currentEditingRoom.value = null
    // 关闭抽屉并销毁组件
    showRoomPricingEditDrawer.value = false
}

const handleRoomPricingCancel = () => {
    currentEditingRoom.value = null
    // 关闭抽屉并销毁组件
    showRoomPricingEditDrawer.value = false
}

// 批量设置免租期成功处理
const handleBatchSetFreeRentSuccess = (freeRentData: any) => {
    // 更新选中房源的免租期信息
    const selectedRooms = tableData.value.filter(item => selectedKeys.value.includes(item.uniqueId))

    selectedRooms.forEach(room => {
        // 更新免租期相关字段
        room.freeRentType = freeRentData.freeRentType
        room.freeRentPeriod = freeRentData.freeRentPeriod
        room.freeRentSteps = freeRentData.freeRentSteps
        room.freeRentTerm = freeRentData.freeRentTerm
    })

    // 更新原始数据
    originalTableData.value = [...tableData.value]

    // 清空选中状态
    selectedKeys.value = []

    // 关闭弹框
    showBatchSetFreeRentModal.value = false

    Message.success(`已为${selectedRooms.length}个房源设置免租期`)
}

// 批量设置免租期取消处理
const handleBatchSetFreeRentCancel = () => {
    showBatchSetFreeRentModal.value = false
}

// 地块选择处理
const handlePlotChange = (plotId: string) => {
    selectedPlotId.value = plotId
    filterTableDataByPlot()
}

// 根据地块筛选表格数据
const filterTableDataByPlot = () => {
    let filteredData = [...originalTableData.value]

    // 根据地块筛选
    if (selectedPlotId.value) {
        filteredData = filteredData.filter(item => item.parcelId === selectedPlotId.value)
    }

    // 根据搜索关键词筛选
    if (searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.trim().toLowerCase()
        filteredData = filteredData.filter(item => {
            const buildingMatch = item.buildingName && item.buildingName.toLowerCase().includes(keyword)
            const roomMatch = item.roomName && item.roomName.toLowerCase().includes(keyword)
            return buildingMatch || roomMatch
        })
    }

    tableData.value = filteredData
    // 重置分页
    tablePagination.current = 1
    tablePagination.total = tableData.value.length
}

// 折扣方案相关方法
const addDiscountScheme = () => {
    const newScheme = {
        id: `discount-${Date.now()}`,
        post: '0',
        discount: null
    }
    formData.discountSchemes.push(newScheme)
}

const removeDiscountScheme = (index: number) => {
    formData.discountSchemes.splice(index, 1)
}

// 暴露方法给父组件
defineExpose({
    show
})
</script>

<style scoped lang="less">


.form-section {
    margin-bottom: 16px;
}

.pricing-info,
.pricing-detail {
    margin-bottom: 16px;
}

.project-info {
    margin-bottom: 16px;
}

.discount-schemes {
    .discount-item {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .add-discount-btn {
        margin-top: 16px;
    }
}

.list-header {
    margin: 16px 0;
}

.list-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.search-filters {
    display: flex;
    align-items: center;
}

.search-box {
    width: 360px;
}

:deep(.arco-table-size-small .arco-table-th) {
    background-color: #f2f3f5;
}

:deep(.arco-form-item-label-col) {
    text-align: right;
}

:deep(.arco-modal-body) {
    padding: 0;
}

.modal-header {
    padding: 16px 16px 0 16px;
}

.modal-content {
    padding: 0 16px 16px 16px;
    height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    position: sticky;
    bottom: 0;
    background: white;
    border-top: 1px solid #e5e6eb;
    padding: 16px 20px;
    text-align: right;
    z-index: 10;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}
.remove-btn{
    margin-bottom: 16px;
    font-size: 18px;
}
</style>