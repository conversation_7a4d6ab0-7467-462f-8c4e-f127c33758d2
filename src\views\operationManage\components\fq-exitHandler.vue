<template>
    <div class="exit-handler">
        <a-tabs v-model:active-key="activeTab" type="card">
            <!-- 退租申请 Tab -->
            <a-tab-pane key="1" title="退租申请">
                <div class="tab-content">
                    <a-table :data="exitHouses" :columns="exitColumns" :pagination="false"
                        :bordered="{ cell: true }"></a-table>
                </div>
            </a-tab-pane>

            <!-- 物业交割 Tab -->
            <a-tab-pane key="2" title="物业交割">
                <div class="tab-content">
                    <!-- 基本信息 -->
                    <section-title title="基本信息" style="margin-bottom: 16px;" />
                    <a-row :gutter="16" class="info-section">
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">合同编号:</span>
                                <span class="value">{{ data?.contractNo }}</span>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">承租方:</span>
                                <span class="value">{{ data?.tenantName }}</span>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">退租类型:</span>
                                <span class="value">{{ data?.exitType }}</span>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">退租日期:</span>
                                <span class="value">{{ data?.exitDate }}</span>
                            </div>
                        </a-col>
                    </a-row>

                    <!-- 物业交割单 -->
                    <div class="section-header">
                        <section-title title="物业交割单" style="margin-bottom: 16px;" />
                        <div class="operation-row">
                            <a-space>

                                <a-button type="primary" size="small" @click="handleBatchConfirm('business')">
                                    商服批量确认
                                </a-button>
                                <a-button type="primary" size="small" @click="handleCopyPropertyConfirmUrl">
                                    复制物业确认单地址
                                </a-button>
                            </a-space>
                        </div>
                    </div>
                    <div>
                        <a-space>
                            <a-checkbox v-model="selectAll" @change="handleSelectAll">全选</a-checkbox>
                            <a-input placeholder="搜索关键字" v-model="searchKeyword" style="width: 200px" />
                            <a-date-picker placeholder="设置出场日期" v-model="batchExitDate" style="width: 200px" />
                        </a-space>
                    </div>

                    <a-collapse :default-active-key="activePropertyKeys" :expand-icon-position="expandPosition"
                        style="margin-top: 12px;">
                        <a-collapse-item v-for="(house, index) in propertyHandoverList" :key="index">
                            <template #header>
                                <div class="property-header">
                                    <div class="left">
                                        <a-checkbox :model-value="house.selected"
                                            @change="(checked: boolean) => handleSelectSingle(house, checked)"></a-checkbox>
                                        <span>{{ house.buildingName }} {{ house.houseName }}</span>
                                    </div>
                                    <div class="right">
                                        <a-tag color="green" v-if="house.businessConfirmed">商服已确认</a-tag>
                                        <a-tag color="blue" v-if="house.propertyConfirmed">物业已确认</a-tag>
                                        <span class="date">出场日期：{{ house.exitDate || '未设置' }}</span>
                                        <span class="delayed" v-if="house.delayDays > 0">延迟{{ house.delayDays }}天</span>
                                    </div>
                                </div>
                            </template>
                            <div class="property-content">
                                <!-- 出场日期选择 -->
                                <a-row :gutter="16" style="margin-bottom: 16px;">
                                    <a-col :span="8">
                                        <a-form-item label="出场日期" :validate-trigger="['change', 'input']"
                                            :rules="[{ required: true, message: '请选择出场日期' }]">
                                            <a-date-picker v-model="house.exitDate" style="width: 100%;"
                                                placeholder="请选择出场日期" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>

                                <!-- 租控管理 -->
                                <a-row :gutter="16" style="margin-bottom: 16px;">
                                    <a-col :span="24">
                                        <a-radio-group v-model="house.rentControl">
                                            <a-radio value="business">租控管理</a-radio>
                                            <a-radio value="property">物业管理</a-radio>
                                        </a-radio-group>
                                    </a-col>
                                </a-row>

                                <!-- 房间配套情况 -->
                                <div class="section-header">
                                    <section-title title="房间配套情况" />
                                    <a-button type="primary" size="small" @click="addAccessory(house)">
                                        <template #icon>
                                            <icon-plus />
                                        </template>
                                        添加配套
                                    </a-button>
                                </div>
                                <a-table :data="house.accessories" :columns="accessoryColumns" :pagination="false"
                                    :bordered="{ cell: true }" row-key="id">
                                    <template #status="{ record }">
                                        <a-select v-model="record.status" style="width: 100px">
                                            <a-option value="normal">正常</a-option>
                                            <a-option value="damaged">损坏</a-option>
                                            <a-option value="compensate">赔偿</a-option>
                                        </a-select>
                                    </template>
                                    <template #compensation="{ record }">
                                        <a-input-number v-model="record.compensation" placeholder="0" :min="0" />
                                    </template>
                                    <template #description="{ record }">
                                        <a-input v-model="record.description" placeholder="说明" />
                                    </template>
                                    <template #operations="{ record }">
                                        <a-button type="text" size="mini"
                                            @click="removeAccessory(house, record)">移除</a-button>
                                    </template>
                                </a-table>

                                <!-- 房屋其他情况 -->
                                <section-title title="房屋其他情况" style="margin-top: 16px;" />
                                <a-form :model="house.houseForm" layout="vertical">
                                    <a-form-item>
                                        <div class="form-item">
                                            <span>门、窗、墙体及其他</span>
                                            <a-radio-group v-model="house.houseForm.doorWindow">
                                                <a-radio value="perfect">完好</a-radio>
                                                <a-radio value="damaged">损坏，赔偿</a-radio>
                                            </a-radio-group>
                                            <a-input-number v-if="house.houseForm.doorWindow === 'damaged'"
                                                v-model="house.houseForm.doorWindowCompensation" placeholder="元从押金中扣除"
                                                :min="0" />
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <div class="form-item">
                                            <span>钥匙交接</span>
                                            <a-radio-group v-model="house.houseForm.keys">
                                                <a-radio value="returned">已交</a-radio>
                                                <a-radio value="notReturned">未交齐，赔偿</a-radio>
                                            </a-radio-group>
                                            <a-input-number v-if="house.houseForm.keys === 'notReturned'"
                                                v-model="house.houseForm.keysCompensation" placeholder="元从押金中扣除"
                                                :min="0" />
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <div class="form-item">
                                            <span>清洁卫生</span>
                                            <a-radio-group v-model="house.houseForm.cleaning">
                                                <a-radio value="clean">自行打扫完毕、洁净</a-radio>
                                                <a-radio value="dirty">需保洁及垃圾清理费</a-radio>
                                            </a-radio-group>
                                            <a-input-number v-if="house.houseForm.cleaning === 'dirty'"
                                                v-model="house.houseForm.cleaningFee" placeholder="元从押金中扣除" :min="0" />
                                        </div>
                                    </a-form-item>
                                </a-form>

                                <!-- 水电物业费情况 -->
                                <section-title title="水电物业费情况" style="margin-top: 16px;" />
                                <a-table :data="house.utilityData" :columns="utilityColumns" :pagination="false"
                                    :bordered="{ cell: true }"></a-table>

                                <!-- 房间照片 -->
                                <section-title title="房间照片" style="margin-top: 16px;" />
                                <a-upload list-type="picture-card" :file-list="house.photos" :custom-request="() => { }"
                                    @change="(info: any) => handlePhotoChange(house, info)">
                                    <div v-if="house.photos.length < 5">
                                        <icon-plus />
                                        <div>上传照片</div>
                                    </div>
                                </a-upload>

                                <!-- 固定资产、设备设施评估情况 -->
                                <section-title title="固定资产、设备设施评估情况" style="margin-top: 16px;" />
                                <a-textarea v-model="house.assetEvaluation" placeholder="请输入评估情况"
                                    :auto-size="{ minRows: 3, maxRows: 6 }" />

                                <!-- 备注 -->
                                <section-title title="备注" style="margin-top: 16px;" />
                                <a-textarea v-model="house.remarks" placeholder="请输入备注信息"
                                    :auto-size="{ minRows: 3, maxRows: 6 }" />

                                <!-- 按钮组 -->
                                <!-- <div class="house-actions">
                                    <a-space>
                                        <a-button type="primary" @click="saveHouseInfo(house, 'confirm')">确认</a-button>
                                        <a-button @click="saveHouseInfo(house, 'temp')">暂存</a-button>
                                        <a-button @click="cancelHouseEdit(house)">取消</a-button>
                                    </a-space>
                                </div> -->
                            </div>
                        </a-collapse-item>
                    </a-collapse>
                </div>
            </a-tab-pane>

            <!-- 费用结算 Tab -->
            <a-tab-pane key="3" title="费用结算" v-if="showSettlementTab">
                <div class="tab-content">
                    <!-- 费用结算信息 -->
                    <div class="section-header">
                        <section-title title="费用结算" />
                        <a-space>
                            <a-button type="primary" size="small" @click="handleViewDelayedHouses">
                                查看延迟出场房源
                            </a-button>
                            <a-button type="primary" size="small" @click="handleAddFeeItem">
                                添加费项
                            </a-button>
                        </a-space>
                    </div>
                    <a-table :data="feeItems" :columns="feeColumns" :pagination="false" :bordered="{ cell: true }"
                        row-key="id">
                        <template #operations="{ record }">
                            <a-button v-if="record.isNew" type="text" size="mini"
                                @click="removeFeeItem(record)">移除</a-button>
                        </template>
                        <template #description="{ record }">
                            <a-input v-model="record.description" placeholder="费用说明" />
                        </template>
                    </a-table>

                    <div class="fee-summary">
                        <div class="total">
                            费用合计：<span class="amount">{{ totalFeeAmount }}</span>元{{ feeDescription }}
                        </div>
                        <div class="reduction">
                            <a-checkbox v-model="feeReduction.checked">是否减免</a-checkbox>
                            <a-space v-if="feeReduction.checked">
                                <a-input-number v-model="feeReduction.amount" placeholder="减免金额" :min="0" />元
                                <a-input v-model="feeReduction.reason" placeholder="减免原因" style="width: 200px" />
                            </a-space>
                        </div>
                        <div class="final-amount">
                            最终费用金额：<span class="amount">{{ finalFeeAmount }}</span>元{{ finalFeeDescription }}
                        </div>
                    </div>

                    <!-- 承租方收款信息 -->
                    <section-title title="承租方收款信息" style="margin: 16px 0;" />
                    <a-form :model="paymentInfo" layout="horizontal" :label-col-props="{ span: 6 }"
                        :wrapper-col-props="{ span: 18 }" label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item label="收款人">
                                    <a-input v-model="paymentInfo.receiver" placeholder="请输入收款人姓名" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="收款账号">
                                    <a-input v-model="paymentInfo.account" placeholder="请输入收款账号" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="开户银行">
                                    <a-input v-model="paymentInfo.bank" placeholder="请输入开户银行" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>

                    <!-- 手续办理情况 -->
                    <section-title title="手续办理情况" style="margin: 16px 0;" />
                    <a-form :model="procedureInfo" layout="horizontal" :label-col-props="{ span: 6 }"
                        :wrapper-col-props="{ span: 18 }" label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item label="营业执照">
                                    <a-select v-model="procedureInfo.businessLicense" placeholder="请选择">
                                        <a-option value="returned">已归还</a-option>
                                        <a-option value="not-returned">未归还</a-option>
                                        <a-option value="none">无</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="税务登记证">
                                    <a-select v-model="procedureInfo.taxRegistration" placeholder="请选择">
                                        <a-option value="returned">已归还</a-option>
                                        <a-option value="not-returned">未归还</a-option>
                                        <a-option value="none">无</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>

                    <div class="settlement-options">
                        <a-radio-group v-model="settlementType">
                            <a-radio value="settlement-only">只结算，暂不退款</a-radio>
                            <a-radio value="settlement-refund">结算并申请退款</a-radio>
                        </a-radio-group>
                    </div>
                </div>
            </a-tab-pane>

            <!-- 物业交割和费用结算 Tab -->
            <a-tab-pane key="4" title="物业交割和费用结算" v-if="showCombinedTab">
                <div class="tab-content">
                    <!-- 物业交割内容 -->
                    <section-title title="物业交割" style="margin-bottom: 16px;" />
                    <!-- 基本信息 -->
                    <a-row :gutter="16" class="info-section">
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">合同编号:</span>
                                <span class="value">{{ data?.contractNo }}</span>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">承租方:</span>
                                <span class="value">{{ data?.tenantName }}</span>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">退租类型:</span>
                                <span class="value">{{ data?.exitType }}</span>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div class="info-item">
                                <span class="label">退租日期:</span>
                                <span class="value">{{ data?.exitDate }}</span>
                            </div>
                        </a-col>
                    </a-row>

                    <!-- 物业交割单 - 与物业交割Tab页相同的内容 -->
                    <div class="section-header">
                        <section-title title="物业交割单" />
                        <div class="operation-row">
                            <a-space>
                                <a-checkbox v-model="selectAll" @change="handleSelectAll" style="width: 80px;">全选</a-checkbox>
                                <a-input placeholder="搜索关键字" v-model="searchKeyword" style="width: 200px" />
                                <a-date-picker placeholder="设置出场日期" v-model="batchExitDate" style="width: 200px" />
                                <a-button type="primary" size="small" @click="handleBatchConfirm('business')">
                                    商服批量确认
                                </a-button>
                                <a-button type="primary" size="small" @click="handleCopyPropertyConfirmUrl">
                                    复制物业确认单地址
                                </a-button>
                            </a-space>
                        </div>
                    </div>

                    <!-- 物业交割房源列表 -->
                    <a-collapse :default-active-key="activePropertyKeys" :expand-icon-position="expandPosition"
                        >
                        <a-collapse-item v-for="(house, index) in propertyHandoverList" :key="index">
                            <template #header>
                                <div class="property-header">
                                    <div class="left">
                                        <a-checkbox :model-value="house.selected"
                                            @change="(checked: boolean) => handleSelectSingle(house, checked)"></a-checkbox>
                                        <span>{{ house.buildingName }} {{ house.houseName }}</span>
                                    </div>
                                    <div class="right">
                                        <a-tag color="green" v-if="house.businessConfirmed">商服已确认</a-tag>
                                        <a-tag color="blue" v-if="house.propertyConfirmed">物业已确认</a-tag>
                                        <span class="date">出场日期：{{ house.exitDate || '未设置' }}</span>
                                        <span class="delayed" v-if="house.delayDays > 0">延迟{{ house.delayDays }}天</span>
                                    </div>
                                </div>
                            </template>
                            <div class="property-content">
                                <!-- 出场日期选择 -->
                                <a-row :gutter="16" style="margin-bottom: 16px;">
                                    <a-col :span="8">
                                        <a-form-item label="出场日期" :validate-trigger="['change', 'input']"
                                            :rules="[{ required: true, message: '请选择出场日期' }]">
                                            <a-date-picker v-model="house.exitDate" style="width: 100%;"
                                                placeholder="请选择出场日期" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>

                                <!-- 租控管理 -->
                                <a-row :gutter="16" style="margin-bottom: 16px;">
                                    <a-col :span="24">
                                        <a-radio-group v-model="house.rentControl">
                                            <a-radio value="business">租控管理</a-radio>
                                            <a-radio value="property">物业管理</a-radio>
                                        </a-radio-group>
                                    </a-col>
                                </a-row>

                                <!-- 房间配套情况 -->
                                <div class="section-header">
                                    <section-title title="房间配套情况" />
                                    <a-button type="primary" size="small" @click="addAccessory(house)">
                                        <template #icon>
                                            <icon-plus />
                                        </template>
                                        添加配套
                                    </a-button>
                                </div>
                                <a-table :data="house.accessories" :columns="accessoryColumns" :pagination="false"
                                    :bordered="{ cell: true }" row-key="id">
                                    <template #status="{ record }">
                                        <a-select v-model="record.status" style="width: 100px">
                                            <a-option value="normal">正常</a-option>
                                            <a-option value="damaged">损坏</a-option>
                                            <a-option value="compensate">赔偿</a-option>
                                        </a-select>
                                    </template>
                                    <template #compensation="{ record }">
                                        <a-input-number v-model="record.compensation" placeholder="0" :min="0" />
                                    </template>
                                    <template #description="{ record }">
                                        <a-input v-model="record.description" placeholder="说明" />
                                    </template>
                                    <template #operations="{ record }">
                                        <a-button type="text" size="mini"
                                            @click="removeAccessory(house, record)">移除</a-button>
                                    </template>
                                </a-table>

                                <!-- 房屋其他情况 -->
                                <section-title title="房屋其他情况" style="margin-top: 16px;" />
                                <a-form :model="house.houseForm" layout="vertical">
                                    <a-form-item>
                                        <div class="form-item">
                                            <span>门、窗、墙体及其他</span>
                                            <a-radio-group v-model="house.houseForm.doorWindow">
                                                <a-radio value="perfect">完好</a-radio>
                                                <a-radio value="damaged">损坏，赔偿</a-radio>
                                            </a-radio-group>
                                            <a-input-number v-if="house.houseForm.doorWindow === 'damaged'"
                                                v-model="house.houseForm.doorWindowCompensation" placeholder="元从押金中扣除"
                                                :min="0" />
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <div class="form-item">
                                            <span>钥匙交接</span>
                                            <a-radio-group v-model="house.houseForm.keys">
                                                <a-radio value="returned">已交</a-radio>
                                                <a-radio value="notReturned">未交齐，赔偿</a-radio>
                                            </a-radio-group>
                                            <a-input-number v-if="house.houseForm.keys === 'notReturned'"
                                                v-model="house.houseForm.keysCompensation" placeholder="元从押金中扣除"
                                                :min="0" />
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <div class="form-item">
                                            <span>清洁卫生</span>
                                            <a-radio-group v-model="house.houseForm.cleaning">
                                                <a-radio value="clean">自行打扫完毕、洁净</a-radio>
                                                <a-radio value="dirty">需保洁及垃圾清理费</a-radio>
                                            </a-radio-group>
                                            <a-input-number v-if="house.houseForm.cleaning === 'dirty'"
                                                v-model="house.houseForm.cleaningFee" placeholder="元从押金中扣除" :min="0" />
                                        </div>
                                    </a-form-item>
                                </a-form>

                                <!-- 水电物业费情况 -->
                                <section-title title="水电物业费情况" style="margin-top: 16px;" />
                                <a-table :data="house.utilityData" :columns="utilityColumns" :pagination="false"
                                    :bordered="{ cell: true }"></a-table>

                                <!-- 房间照片 -->
                                <section-title title="房间照片" style="margin-top: 16px;" />
                                <a-upload list-type="picture-card" :file-list="house.photos" :custom-request="() => { }"
                                    @change="(info: any) => handlePhotoChange(house, info)">
                                    <div v-if="house.photos.length < 5">
                                        <icon-plus />
                                        <div>上传照片</div>
                                    </div>
                                </a-upload>

                                <!-- 固定资产、设备设施评估情况 -->
                                <section-title title="固定资产、设备设施评估情况" style="margin-top: 16px;" />
                                <a-textarea v-model="house.assetEvaluation" placeholder="请输入评估情况"
                                    :auto-size="{ minRows: 3, maxRows: 6 }" />

                                <!-- 备注 -->
                                <section-title title="备注" style="margin-top: 16px;" />
                                <a-textarea v-model="house.remarks" placeholder="请输入备注信息"
                                    :auto-size="{ minRows: 3, maxRows: 6 }" />

                                <!-- 按钮组 -->
                                <!-- <div class="house-actions">
                                    <a-space>
                                        <a-button type="primary" @click="saveHouseInfo(house, 'confirm')">确认</a-button>
                                        <a-button @click="saveHouseInfo(house, 'temp')">暂存</a-button>
                                        <a-button @click="cancelHouseEdit(house)">取消</a-button>
                                    </a-space>
                                </div> -->
                            </div>
                        </a-collapse-item>
                    </a-collapse>

                    <!-- 费用结算信息 -->
                    <div class="section-header" >
                        <section-title title="费用结算" />
                        <a-space>
                            <a-button type="primary" size="small" @click="handleViewDelayedHouses">
                                查看延迟出场房源
                            </a-button>
                            <a-button type="primary" size="small" @click="handleAddFeeItem">
                                添加费项
                            </a-button>
                        </a-space>
                    </div>

                    <!-- 费用表格 -->
                    <a-table :data="feeItems" :columns="feeColumns" :pagination="false" :bordered="{ cell: true }"
                        row-key="id">
                        <template #operations="{ record }">
                            <a-button v-if="record.isNew" type="text" size="mini"
                                @click="removeFeeItem(record)">移除</a-button>
                        </template>
                        <template #description="{ record }">
                            <a-input v-model="record.description" placeholder="费用说明" />
                        </template>
                    </a-table>

                    <!-- 费用汇总 -->
                    <div class="fee-summary">
                        <div class="total">
                            费用合计：<span class="amount">{{ totalFeeAmount }}</span>元{{ feeDescription }}
                        </div>
                        <div class="reduction">
                            <a-checkbox v-model="feeReduction.checked">是否减免</a-checkbox>
                            <a-space v-if="feeReduction.checked">
                                <a-input-number v-model="feeReduction.amount" placeholder="减免金额" :min="0" />元
                                <a-input v-model="feeReduction.reason" placeholder="减免原因" style="width: 200px" />
                            </a-space>
                        </div>
                        <div class="final-amount">
                            最终费用金额：<span class="amount">{{ finalFeeAmount }}</span>元{{ finalFeeDescription }}
                        </div>
                    </div>

                    <!-- 承租方收款信息 -->
                    <section-title title="承租方收款信息" style="margin: 16px 0;" />
                    <a-form :model="paymentInfo" layout="horizontal" :label-col-props="{ span: 6 }"
                        :wrapper-col-props="{ span: 18 }" label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item label="收款人">
                                    <a-input v-model="paymentInfo.receiver" placeholder="请输入收款人姓名" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="收款账号">
                                    <a-input v-model="paymentInfo.account" placeholder="请输入收款账号" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="开户银行">
                                    <a-input v-model="paymentInfo.bank" placeholder="请输入开户银行" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>

                    <!-- 手续办理情况 -->
                    <section-title title="手续办理情况" style="margin-bottom: 16px;" />
                    <a-form :model="procedureInfo" layout="horizontal" :label-col-props="{ span: 6 }"
                        :wrapper-col-props="{ span: 18 }" label-align="right">
                        <a-row :gutter="16">
                            <a-col :span="8">
                                <a-form-item label="营业执照">
                                    <a-select v-model="procedureInfo.businessLicense" placeholder="请选择">
                                        <a-option value="returned">已归还</a-option>
                                        <a-option value="not-returned">未归还</a-option>
                                        <a-option value="none">无</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item label="税务登记证">
                                    <a-select v-model="procedureInfo.taxRegistration" placeholder="请选择">
                                        <a-option value="returned">已归还</a-option>
                                        <a-option value="not-returned">未归还</a-option>
                                        <a-option value="none">无</a-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>

                    <div class="settlement-options">
                        <a-radio-group v-model="settlementType">
                            <a-radio value="settlement-only">只结算，暂不退款</a-radio>
                            <a-radio value="settlement-refund">结算并申请退款</a-radio>
                        </a-radio-group>
                    </div>
                </div>
            </a-tab-pane>
        </a-tabs>

        <!-- 底部按钮 -->
        <div class="footer-actions">
            <a-space>
                <a-button @click="handleCancel">取消</a-button>
                <a-button type="primary" @click="handleConfirm">确认</a-button>
            </a-space>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import sectionTitle from '@/components/sectionTitle/index.vue'

// 定义数据类型
interface ExitHouseItem {
    id: string
    exitType: string
    exitDate: string
    exitHouses: string
    houseCount: number
    [key: string]: any
}

interface PropertyHouseItem {
    id: string
    index: number
    buildingName: string
    houseName: string
    selected: boolean
    businessConfirmed: boolean
    propertyConfirmed: boolean
    exitDate: string | null
    delayDays: number
    rentControl: string
    accessories: AccessoryItem[]
    houseForm: HouseFormItem
    utilityData: UtilityItem[]
    photos: any[]
    assetEvaluation: string
    remarks: string
    [key: string]: any
}

interface AccessoryItem {
    id: string
    index: number
    type: string
    name: string
    specification: string
    quantity: number
    status: string
    compensation: number
    description: string
}

interface HouseFormItem {
    doorWindow: string
    doorWindowCompensation: number
    keys: string
    keysCompensation: number
    cleaning: string
    cleaningFee: number
}

interface UtilityItem {
    type: string
    electric: string
    coldWater: string
    hotWater: string
    propertyFee: string
}

interface FeeItem {
    id: string
    index: number
    type: string
    subject: string
    amount: number
    period: string
    description: string
    isNew?: boolean
}

// 定义props和emits
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    mode: {
        type: String,
        default: 'property-only' // 'property-only' 或 'property-and-settlement'
    }
})

const emit = defineEmits(['cancel', 'save'])

// Tab状态
const activeTab = ref('1')
const showSettlementTab = ref(false)
const showCombinedTab = ref(false)

// 退租申请数据
const exitHouses = ref<ExitHouseItem[]>([])
const exitColumns = [
    {
        title: '退租类型',
        dataIndex: 'exitType',
        width: 150
    },
    {
        title: '退租日期',
        dataIndex: 'exitDate',
        width: 150
    },
    {
        title: '退租房源',
        dataIndex: 'exitHouses',
        width: 250
    },
    {
        title: '房源数',
        dataIndex: 'houseCount',
        width: 100
    }
]

// 物业交割数据
const expandPosition = ref('right')
const activePropertyKeys = ref([0])
const propertyHandoverList = ref<PropertyHouseItem[]>([])
const selectAll = ref(false)
const searchKeyword = ref('')
const batchExitDate = ref<string | null>(null)

// 配套列表
const accessoryColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 50,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '种类',
        dataIndex: 'type',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '物品名称',
        dataIndex: 'name',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '规格',
        dataIndex: 'specification',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '数量',
        dataIndex: 'quantity',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '现状',
        slotName: 'status',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '赔偿金(元)',
        slotName: 'compensation',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '说明',
        slotName: 'description',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 水电物业费
const utilityColumns = [
    {
        title: '',
        dataIndex: 'type',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '电表',
        dataIndex: 'electric',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '冷水表',
        dataIndex: 'coldWater',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '热水表',
        dataIndex: 'hotWater',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '物业费',
        dataIndex: 'propertyFee',
        width: 150,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 费用结算数据
const feeItems = ref<FeeItem[]>([])
const feeColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 50,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '收支类型',
        dataIndex: 'type',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '费用科目',
        dataIndex: 'subject',
        width: 120,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '金额',
        dataIndex: 'amount',
        width: 100,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '费用周期',
        dataIndex: 'period',
        width: 180,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '费用说明',
        slotName: 'description',
        width: 200,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '操作',
        slotName: 'operations',
        width: 80,
        align: 'center',
        ellipsis: true,
        tooltip: true,
    }
]

// 减免数据
const feeReduction = reactive({
    checked: false,
    amount: 0,
    reason: ''
})

// 结算类型
const settlementType = ref('settlement-only')

// 收款信息
const paymentInfo = reactive({
    receiver: '',
    account: '',
    bank: ''
})

// 手续办理情况
const procedureInfo = reactive({
    businessLicense: 'none',
    taxRegistration: 'none'
})

// 计算总费用
const totalFeeAmount = computed(() => {
    let total = 0
    feeItems.value.forEach(item => {
        if (item.type === '支') {
            total -= item.amount
        } else {
            total += item.amount
        }
    })
    return total
})

// 费用描述
const feeDescription = computed(() => {
    if (totalFeeAmount.value > 0) {
        return `（应收承租方${Math.abs(totalFeeAmount.value)}元）`
    } else if (totalFeeAmount.value < 0) {
        return `（应退承租方${Math.abs(totalFeeAmount.value)}元）`
    }
    return ''
})

// 最终费用金额
const finalFeeAmount = computed(() => {
    if (feeReduction.checked) {
        return totalFeeAmount.value - feeReduction.amount
    }
    return totalFeeAmount.value
})

// 最终费用描述
const finalFeeDescription = computed(() => {
    if (finalFeeAmount.value > 0) {
        return `（应收承租方${Math.abs(finalFeeAmount.value)}元）`
    } else if (finalFeeAmount.value < 0) {
        return `（应退承租方${Math.abs(finalFeeAmount.value)}元）`
    }
    return ''
})

// 生命周期
onMounted(() => {
    initData()
})

// 初始化数据
const initData = () => {
    // 根据模式设置Tab显示
    if (props.mode === 'property-only') {
        showSettlementTab.value = true
        showCombinedTab.value = false
    } else { // 'property-and-settlement'
        showSettlementTab.value = false
        showCombinedTab.value = true
    }

    // 初始化退租申请数据
    if (props.data) {
        exitHouses.value = [{
            id: '1',
            exitType: props.data.exitType || '到期退租',
            exitDate: props.data.exitDate || '',
            exitHouses: props.data.exitHouses || '',
            houseCount: props.data.houseCount || 0
        }]
    }

    // 初始化物业交割数据
    initPropertyHandoverList()

    // 初始化费用结算数据
    initFeeItems()
}

// 初始化物业交割房源列表
const initPropertyHandoverList = () => {
    // 实际项目中应该根据房源数据初始化
    const mockList: PropertyHouseItem[] = []
    for (let i = 1; i <= 3; i++) {
        mockList.push({
            id: `house-${i}`,
            index: i,
            buildingName: `A${i}栋`,
            houseName: `A${i}-${100 + i}室`,
            selected: false,
            businessConfirmed: false,
            propertyConfirmed: false,
            exitDate: null,
            delayDays: i * 2,
            rentControl: 'business',
            accessories: [],
            houseForm: {
                doorWindow: 'perfect',
                doorWindowCompensation: 0,
                keys: 'returned',
                keysCompensation: 0,
                cleaning: 'clean',
                cleaningFee: 0
            },
            utilityData: [
                { type: '读数', electric: '', coldWater: '', hotWater: '', propertyFee: '' },
                { type: '欠费', electric: '', coldWater: '', hotWater: '', propertyFee: '' }
            ],
            photos: [],
            assetEvaluation: '',
            remarks: ''
        })
    }
    propertyHandoverList.value = mockList
}

// 初始化费用结算数据
const initFeeItems = () => {
    feeItems.value = [
        {
            id: '1',
            index: 1,
            type: '支',
            subject: '保证金',
            amount: 10000,
            period: '2023-01-01 至 2023-12-31',
            description: '退保证金'
        },
        {
            id: '2',
            index: 2,
            type: '收',
            subject: '赔偿金',
            amount: 500,
            period: '2023-01-01 至 2023-12-31',
            description: '设备损坏赔偿'
        }
    ]
}

// 全选处理
const handleSelectAll = (checked: boolean) => {
    propertyHandoverList.value.forEach(house => {
        house.selected = checked
    })
}

// 单选处理
const handleSelectSingle = (house: PropertyHouseItem, checked: boolean) => {
    house.selected = checked
    // 更新全选状态
    selectAll.value = propertyHandoverList.value.every(item => item.selected)
}

// 商服批量确认
const handleBatchConfirm = (type: 'business' | 'property') => {
    const selectedHouses = propertyHandoverList.value.filter(house => house.selected)
    if (selectedHouses.length === 0) {
        Message.warning('请先选择房源')
        return
    }

    if (type === 'business') {
        selectedHouses.forEach(house => {
            house.businessConfirmed = true
        })
        Message.success('商服批量确认成功')
    } else {
        selectedHouses.forEach(house => {
            house.propertyConfirmed = true
        })
        Message.success('物业批量确认成功')
    }
}

// 复制物业确认单地址
const handleCopyPropertyConfirmUrl = () => {
    const selectedHouses = propertyHandoverList.value.filter(house => house.selected)
    if (selectedHouses.length === 0) {
        Message.warning('请先选择房源')
        return
    }

    // 这里应该是实际的URL生成和复制逻辑
    const url = `https://example.com/property-confirmation?ids=${selectedHouses.map(h => h.id).join(',')}`
    // 复制到剪贴板的逻辑

    Message.success('物业确认单地址已复制到剪贴板')
}

// 添加配套
const addAccessory = (house: PropertyHouseItem) => {
    const newId = `accessory-${house.id}-${Date.now()}`
    house.accessories.push({
        id: newId,
        index: house.accessories.length + 1,
        type: '',
        name: '',
        specification: '',
        quantity: 1,
        status: 'normal',
        compensation: 0,
        description: ''
    })
}

// 移除配套
const removeAccessory = (house: PropertyHouseItem, record: AccessoryItem) => {
    const index = house.accessories.findIndex(item => item.id === record.id)
    if (index !== -1) {
        house.accessories.splice(index, 1)
        // 重新计算序号
        house.accessories.forEach((item, idx) => {
            item.index = idx + 1
        })
    }
}

// 照片变更处理
const handlePhotoChange = (house: PropertyHouseItem, info: any) => {
    house.photos = info.fileList
}

// 保存房源信息
const saveHouseInfo = (house: PropertyHouseItem, saveType: 'confirm' | 'temp') => {
    if (saveType === 'confirm') {
        if (!house.exitDate) {
            Message.warning('请选择出场日期')
            return
        }
        house.businessConfirmed = true
        Message.success('房源交割信息已确认')
    } else {
        Message.success('房源交割信息已暂存')
    }
}

// 取消房源编辑
const cancelHouseEdit = (house: PropertyHouseItem) => {
    // 重置为初始值或最后保存的值
    Message.info('已取消编辑')
}

// 查看延迟出场房源
const handleViewDelayedHouses = () => {
    const delayedHouses = propertyHandoverList.value.filter(house => house.delayDays > 0)
    if (delayedHouses.length === 0) {
        Message.info('没有延迟出场的房源')
        return
    }

    // 显示延迟房源信息
    Modal.info({
        title: '延迟出场房源',
        content: delayedHouses.map(house =>
            `${house.buildingName} ${house.houseName}：延迟${house.delayDays}天`
        ).join('<br>'),
        maskClosable: true
    })
}

// 添加费项
const handleAddFeeItem = () => {
    const newId = `fee-${Date.now()}`
    feeItems.value.push({
        id: newId,
        index: feeItems.value.length + 1,
        type: '收',
        subject: '',
        amount: 0,
        period: '',
        description: '',
        isNew: true
    })
}

// 移除费项
const removeFeeItem = (record: FeeItem) => {
    const index = feeItems.value.findIndex(item => item.id === record.id)
    if (index !== -1) {
        feeItems.value.splice(index, 1)
        // 重新计算序号
        feeItems.value.forEach((item, idx) => {
            item.index = idx + 1
        })
    }
}

// 取消
const handleCancel = () => {
    emit('cancel')
}

// 确认
const handleConfirm = () => {
    // 验证数据
    if (activeTab.value === '1') {
        // 退租申请验证
    } else if (activeTab.value === '2' || activeTab.value === '4') {
        // 物业交割验证
        const unconfirmedHouses = propertyHandoverList.value.filter(house => !house.businessConfirmed || !house.propertyConfirmed)
        if (unconfirmedHouses.length > 0) {
            Message.warning('有未确认的物业交割单，请确认')
            return
        }
    }

    if (activeTab.value === '3' || activeTab.value === '4') {
        // 费用结算验证
        if (finalFeeAmount.value < 0 && !paymentInfo.receiver) {
            Message.warning('应退款项，请填写承租方收款信息')
            return
        }
    }

    // 提交数据
    const formData: any = {
        contractId: props.data?.contractId,
        mode: props.mode,
        activeTab: activeTab.value
    }

    // 根据当前tab添加数据
    if (activeTab.value === '2' || activeTab.value === '4') {
        formData.propertyHandover = propertyHandoverList.value.map(house => ({
            houseId: house.id,
            exitDate: house.exitDate,
            businessConfirmed: house.businessConfirmed,
            propertyConfirmed: house.propertyConfirmed,
            rentControl: house.rentControl,
            accessories: house.accessories,
            houseForm: house.houseForm,
            utilityData: house.utilityData,
            photos: house.photos,
            assetEvaluation: house.assetEvaluation,
            remarks: house.remarks
        }))
    }

    if (activeTab.value === '3' || activeTab.value === '4') {
        formData.feeSettlement = {
            feeItems: feeItems.value,
            totalAmount: totalFeeAmount.value,
            reduction: feeReduction,
            finalAmount: finalFeeAmount.value,
            paymentInfo: paymentInfo,
            procedureInfo: procedureInfo,
            settlementType: settlementType.value
        }
    }

    emit('save', formData)
}
</script>

<style scoped>
.exit-handler {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tab-content {
    height: calc(100vh - 160px);
    /* padding: 16px 0; */
    flex: 1;
    overflow-y: auto;
}

.info-section {
    /* margin-bottom: 24px; */
    box-sizing: border-box;
    padding: 0 16px;
}

.info-item {
    margin-bottom: 16px;
    display: flex;
}

.label {
    color: #86909c;
    margin-right: 8px;
    min-width: 80px;
}

.value {
    color: #1d2129;
    flex: 1;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-bottom: 12px; */
}

.operation-row {
    margin-bottom: 16px;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.property-header .left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.property-header .right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date {
    color: #86909c;
    font-size: 14px;
}

.delayed {
    color: #f53f3f;
    font-size: 14px;
}

.form-item {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
}

.house-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
}

.fee-summary {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.total,
.final-amount {
    text-align: right;
    font-size: 14px;
}

.amount {
    color: #f53f3f;
    font-weight: bold;
}

.reduction {
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: flex-end;
}

.settlement-options {
    margin-top: 24px;
    display: flex;
    justify-content: center;
}

.footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    /* border-top: 1px solid #e5e6eb; */
}
.section-header {
    box-sizing: border-box ;
    padding-right: 16px ;
}
.section-title {
    margin-bottom: 16px !important;
}
</style>