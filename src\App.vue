<template>
    <a-config-provider :locale="locale">
        <router-view />
        <global-setting />
    </a-config-provider>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import enUS from '@arco-design/web-vue/es/locale/lang/en-us';
import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn';
import GlobalSetting from '@/components/global-setting/index.vue';
import useLocale from '@/hooks/locale';

const { currentLocale } = useLocale();
const locale = computed(() => {
    switch (currentLocale.value) {
        case 'zh-CN':
            return zhCN;
        case 'en-US':
            return enUS;
        default:
            return enUS;
    }
})
</script>
<style lang="less">
.common-flex {
    display: flex;
    align-items: center;
}

.common-flex-column {
    flex-direction: column;
}

.common-flex-center {
    justify-content: center;
}

.common-flex-column-center {
    justify-content: center;
}

.common-tag {
    margin-left: 16px;
}
.common-page {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0 12px 12px 12px;
    overflow: hidden;
}

.common-card {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    // padding: 12px;
    border-radius: 4px;
    background-color: #fff;
}

.common-required {
    color: rgb(var(--red-6));
    font-size: 14px;
    font-weight: 600;
    // display: inline-block;
    // margin-bottom: -12px;
}

.common-header {
    display: flex;
    align-items: center;
    height: 38px;

    .common-header-title {
        font-size: 14px;
        font-weight: 600;
        color: rgb(var(--gray-9));
    }
}

.common-header-tag {
    width: 4px;
    height: 16px;
    background-color: rgb(var(--arcoblue-6));
    border-radius: 4px;
    margin-right: 8px;
}

.common-no-data {
    height: calc(100vh - 180px);
}

.arco-modal-simple {
    padding: 0 !important;

    .arco-modal-header .arco-modal-title {
        padding: 6px 0 !important;
    }

    .arco-modal-body {
        padding: 16px !important;
    }

    .arco-modal-footer {
        padding: 0 0 16px 0 !important;
    }
}

// // .arco-menu-horizontal .arco-menu-inner
// :deep(.arco-menu-horizontal .arco-menu-inner) {
//     // .arco-menu-inner {
//         padding: 6px 12px !important;
//     // }
// }
.arco-drawer-header {
    background-color: rgb(var(--arcoblue-6)) !important;

    .arco-drawer-title {
        color: #fff !important;
    }

    .arco-drawer-close-btn {
        color: #fff !important;
    }

    .arco-icon-hover {
        color: #fff !important;

        &:hover {
            color: #fff !important;
            // background-color: transparent !important;
        }
    }

    .arco-icon-hover:hover::before {
        background-color: rgb(var(--arcoblue-4)) !important;
    }
}

.arco-modal-header {
    background-color: rgb(var(--arcoblue-6)) !important;

    .arco-modal-title {
        color: #fff !important;
    }

    .arco-modal-close-btn {
        color: #fff !important;
    }
}

.arco-card-body {
    padding: 16px 16px;
}

.common-drawer {
    .arco-drawer {
        width: 90% !important;
    }
}

.common-drawer-small {
    .arco-drawer {
        width: 50% !important;
    }
}

.common-modal-max {
    .arco-modal {
        width: 60% !important;
    }

    .arco-modal-body {
        padding: 16px !important;
    }

    .arco-modal-footer {
        padding: 12px 0 !important;
    }
}

.common-modal-no-padding {
    .arco-modal-body {
        padding: 0 !important;
    }
}

.common-table-form-item {
    margin-bottom: 0 !important;
    .arco-form-item-label-col {
        padding-right: 0 !important;
    }
}


// 全局样式
.arco-textarea {
    background-color: #fff !important;
    border: 1px solid var(--color-border-2) !important;
}
.arco-input-wrapper,
.arco-textarea-wrapper{
    background-color: #fff !important;
    border: 1px solid var(--color-border-2);
}

.arco-input-wrapper:hover,
.arco-textarea-wrapper:hover {
    background-color: #fff !important;
    border: 1px solid var(--color-border-2);
}

.arco-select-view-single {
    background-color: #fff !important;
    border: 1px solid var(--color-border-2);
}

.arco-select-view-single:hover {
    background-color: #fff !important;
    border: 1px solid var(--color-border-2);
}

.arco-picker{
    background-color: #fff !important;
    border: 1px solid var(--color-border-2) !important;
}
.arco-picker:hover  {
    background-color: #fff !important;
    border: 1px solid var(--color-border-2) !important;
}
.arco-picker-disabled, .arco-picker-disabled:hover {
    background-color: var(--color-fill-2) !important;
    cursor: not-allowed !important;
    input {
        -webkit-text-fill-color: var(--color-text-2) !important;
    }
}

.arco-select-view-multiple {
    background-color: #fff;
    border: 1px solid var(--color-border-2);
}
.arco-select-view-multiple:hover {
    background-color: #fff;
    border: 1px solid var(--color-border-2);
}

.arco-input-disabled,
.arco-textarea-disabled {
    background-color: var(--color-fill-2) !important;
    cursor: not-allowed;
    input,textarea {
        -webkit-text-fill-color: var(--color-text-2) !important;
    }
}

.arco-select-view-disabled {
    background-color: var(--color-fill-2) !important;
    cursor: not-allowed !important;

    .arco-select-view-value {
        color: var(--color-text-2) !important;
        -webkit-text-fill-color: var(--color-text-2) !important;
    }
}
.arco-select-view-single.arco-select-view-error {
    border: 1px solid var(--color-border-2) !important;
    border-color: rgb(var(--red-6)) !important;
}
.arco-input-wrapper.arco-input-error {
    border: 1px solid var(--color-border-2) !important;
    border-color: rgb(var(--red-6)) !important;
}
</style>
